# 护理记录单菜单添加说明

## 概述
本文档说明如何将床头卡右键菜单中的"护理记录单"功能添加到顶部菜单"日常工作"中。

## 功能说明
- **目标**：在住院护理站顶部菜单栏中添加"日常工作"菜单，并在其下添加"护理记录单"菜单项
- **效果**：用户可以直接从顶部菜单访问护理记录单，无需通过床头卡右键菜单
- **兼容性**：与现有床头卡右键菜单功能完全兼容，不影响原有功能

## 实施方案

### 方案一：使用图形化工具（推荐）

1. **编译项目**
   ```
   确保 Tjhis_Nurinp_Station 项目编译成功
   ```

2. **调用菜单更新工具**
   ```csharp
   // 在任何窗体中调用
   Tjhis.Nurinp.Station.Tools.MenuUpdateHelper.ShowMenuUpdateTool();
   ```

3. **执行更新**
   - 点击"执行菜单更新"按钮
   - 系统会自动创建必要的菜单项
   - 查看操作结果

4. **重启应用程序**
   - 关闭住院护理站应用程序
   - 重新启动应用程序
   - 查看顶部菜单栏是否出现"日常工作"菜单

### 方案二：使用代码调用

```csharp
// 快速更新（推荐）
bool success = Tjhis.Nurinp.Station.Tools.MenuUpdateHelper.QuickUpdateMenu();

// 或者使用底层API
bool success = Tjhis.Nurinp.Station.Service.SimpleMenuUpdater.AddNursingRecordToMainMenu();
```

### 方案三：直接执行SQL脚本

1. **连接Oracle数据库**
2. **执行SQL脚本**
   ```sql
   -- 执行 AddNursingRecordMenu.sql 文件中的SQL语句
   ```

## 技术细节

### 数据库表结构
菜单数据存储在 `COMM.SEC_MENUS_DICT` 表中，主要字段：
- `APPLICATION_CODE`: 应用代码 ('NURINP')
- `MENU_NAME`: 菜单名称 (唯一标识)
- `MENU_TEXT`: 显示文本
- `SERIAL_NO`: 排序序号
- `SUPPER_MENU`: 父菜单名称
- `OPEN_FORM`: 要打开的窗体类名
- `OPEN_FILE_NAME`: 程序集名称

### 添加的菜单项

#### 1. 日常工作主菜单
```sql
MENU_NAME: NURINP_MAIN05
MENU_TEXT: 日常工作
SERIAL_NO: 500
SUPPER_MENU: parent
```

#### 2. 护理记录单菜单项
```sql
MENU_NAME: NURINP_MAIN0570
MENU_TEXT: 护理记录单
SERIAL_NO: 570
SUPPER_MENU: NURINP_MAIN05
OPEN_FORM: TjhisAppPatientView.NursingRecordSheet.frmNursingRecord
OPEN_FILE_NAME: TjhisAppPatientView.dll
```

### 窗体调用
护理记录单窗体位于：
- **程序集**: `TjhisAppPatientView.dll`
- **命名空间**: `TjhisAppPatientView.NursingRecordSheet`
- **类名**: `frmNursingRecord`

## 验证方法

### 1. 数据库验证
```sql
SELECT 
    APPLICATION_CODE AS 应用代码,
    MENU_NAME AS 菜单名称,
    MENU_TEXT AS 显示文本,
    SERIAL_NO AS 序号,
    SUPPER_MENU AS 父菜单,
    OPEN_FORM AS 打开窗体
FROM COMM.SEC_MENUS_DICT 
WHERE APPLICATION_CODE = 'NURINP' 
AND (MENU_NAME = 'NURINP_MAIN05' OR MENU_NAME = 'NURINP_MAIN0570')
ORDER BY SERIAL_NO;
```

### 2. 程序验证
```csharp
string result = Tjhis.Nurinp.Station.Service.SimpleMenuUpdater.ValidateMenu();
MessageBox.Show(result);
```

### 3. 界面验证
重启应用程序后，检查：
- 顶部菜单栏是否出现"日常工作"菜单
- "日常工作"菜单下是否有"护理记录单"菜单项
- 点击"护理记录单"是否能正常打开窗体

## 故障排除

### 常见问题

1. **菜单不显示**
   - 确认数据库中菜单记录已正确添加
   - 确认应用程序已完全重启
   - 检查用户权限设置

2. **点击菜单无反应**
   - 确认 `TjhisAppPatientView.dll` 文件存在
   - 确认窗体类名正确
   - 检查程序集加载是否正常

3. **数据库连接失败**
   - 确认Oracle数据库连接正常
   - 确认用户有 `COMM.SEC_MENUS_DICT` 表的读写权限

### 日志记录
系统会在以下位置记录操作日志：
```
..\Client\LOG\exLOG\MenuUpdate_YYYYMMDD.log
```

日志格式：
```
[2025-01-20 21:02:07] [INFO] 开始执行菜单更新
[2025-01-20 21:02:08] [INFO] ✓ 菜单更新SQL执行成功
[2025-01-20 21:02:09] [ERROR] 连接数据库失败: ORA-12154
```

## 回滚方法

如需撤销更改，执行以下SQL：
```sql
-- 删除护理记录单菜单项
DELETE FROM COMM.SEC_MENUS_DICT 
WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN0570';

-- 删除日常工作主菜单（如果不需要）
DELETE FROM COMM.SEC_MENUS_DICT 
WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN05';

COMMIT;
```

## 注意事项

1. **备份数据库**：执行前建议备份 `COMM.SEC_MENUS_DICT` 表
2. **测试环境**：建议先在测试环境验证功能
3. **用户通知**：提醒用户重启应用程序查看新菜单
4. **权限检查**：确保相关用户有访问新菜单的权限
5. **版本兼容**：确保与 DevExpress 19.1 和 VS2017 兼容

## 联系支持

如遇到问题，请提供以下信息：
- 错误消息截图
- 数据库查询结果
- 日志文件内容
- 操作系统和数据库版本信息
