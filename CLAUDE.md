核心规则 (Core Rules)：
1. 语言与交互规范
   - 必须使用中文简体回复所有内容
   - 所有技术决策都基于 Claude 4 (Opus 4.1/Sonnet 4) 的能力

2. 开发环境约束
   - 编译器: Visual Studio 2017 (不要尝试编译代码)
   - 数据库: Oracle
   - UI控件: DevExpress 19.1
   - 操作系统: Windows 10 专业版

3. 代码安全原则
   - 禁止覆盖现有逻辑 - 必须通过新增方法或扩展现有方法实现功能
   - 保持向后兼容性 - 确保修改不影响现有功能
   - 最小化风险 - 优先选择安全的实现方案

操作指导 (Operational Guidelines)：
4. 代码修改策略
   
   ✅ 推荐做法:
   - 创建新的方法或类
   - 扩展现有接口
   - 添加可选参数
   - 使用条件判断保护原逻辑

   ❌ 避免做法:
   - 直接替换现有方法体
   - 删除现有代码
   - 修改核心业务逻辑
   - 破坏现有API契约

5. 调试与日志规范
   - 日志路径: ..\Client\LOG\exLOG\
   
   - 文件命名: 按时间分割，格式建议：
     * 应用名_YYYYMMDD.log          // 按日分割
     * 应用名_YYYYMMDD_HH.log       // 按小时分割（高频测试）
     * 应用名_测试场景_YYYYMMDD.log  // 按测试场景分割
   
   - 日志级别: 使用分级日志 (ERROR/WARN/INFO/DEBUG)
   
   - 日志格式: 建议包含时间戳、级别、模块、消息
     示例:
     [2025-07-20 21:02:07] [INFO] [药品管理] 开始加载药品列表
     [2025-07-20 21:02:08] [DEBUG] [数据访问] 执行SQL: SELECT * FROM...
     [2025-07-20 21:02:09] [ERROR] [异常处理] 连接数据库失败: ORA-12154
   
   - 文件管理: 
     * 自动清理超过30天的旧日志文件
     * 单个日志文件大小不超过10MB
     * 提供日志压缩归档功能
   
   - 异常处理: 必须包含完整的异常信息和上下文

6. 文档与示例获取
   - 优先级: Context7 MCP > 官方文档 > 社区资源
   - 如果需要进行测试，你需要自动调用 playwright MCP
   - 如果你在分析问题时，你需要自动调用 sequential-thinking MCP
   - 版本匹配: 确保获取的文档与 DevExpress 19.1 兼容
   - 代码示例: 提供完整可运行的示例代码

7. 质量保证
   - 代码审查: 每次修改都要说明潜在影响
   - 测试建议: 提供测试用例或验证步骤
   - 回滚方案: 说明如何撤销修改


特殊说明 (Special Notes)：
- 当需要获取技术文档时，明确说明将使用 Context7 MCP
- 所有代码建议都要考虑 VS2017 和 DevExpress 19.1 的兼容性
- 提供的解决方案要包含详细的实施步骤和注意事项
- 每次测试都有时间，日志文件必须按时间分割以便查看和管理


