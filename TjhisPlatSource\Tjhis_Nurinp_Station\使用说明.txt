护理记录单菜单添加 - 使用说明
=====================================

目标：将床头卡右键菜单中的"护理记录单"添加到顶部菜单"日常工作"中

操作步骤：
1. 连接Oracle数据库
2. 执行SQL脚本：AddNursingRecordMenu.sql
3. 重启住院护理站应用程序

就这么简单！

验证结果：
重启后，在顶部菜单栏会看到"日常工作"菜单，点击后显示"护理记录单"子菜单项。

回滚方法：
如需删除添加的菜单，执行以下SQL：

DELETE FROM CPR.MR_APP_ENTRY
WHERE APP_CODE = 'NURINP'
AND NODE_CODE IN ('NURINP_MAIN05', 'NURINP_MAIN0570');
COMMIT;

注意事项：
1. 执行前建议备份数据库
2. 添加菜单后必须重启应用程序
3. 确保用户有访问新菜单的权限
4. 使用正确的表名：CPR.MR_APP_ENTRY
5. 脚本会自动获取现有NURINP菜单的医院编码
6. 如果没有现有菜单，会使用默认医院编码 'DEFAULT'

技术说明：
- 表名：CPR.MR_APP_ENTRY
- 主菜单：日常工作 (NURINP_MAIN05)
- 子菜单：护理记录单 (NURINP_MAIN0570)
- 调用窗体：TjhisAppPatientView.NursingRecordSheet.frmNursingRecord
- 程序集：TjhisAppPatientView.dll
