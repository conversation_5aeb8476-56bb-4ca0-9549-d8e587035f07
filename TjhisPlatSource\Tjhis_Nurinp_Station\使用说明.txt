护理记录单菜单添加 - 使用说明
=====================================

目标：将床头卡右键菜单中的"护理记录单"添加到顶部菜单"日常工作"中

操作步骤：
1. 连接Oracle数据库
2. 执行SQL脚本：AddNursingRecordMenu.sql
3. 重启住院护理站应用程序

就这么简单！

验证结果：
重启后，在顶部菜单栏会看到"日常工作"菜单，点击后显示"护理记录单"子菜单项。

回滚方法：
如需删除添加的菜单，执行以下SQL：

DELETE FROM MR_APP_ENTRY 
WHERE APP_CODE = 'NURINP' 
AND NODE_CODE IN ('NURINP_MAIN05', 'NURINP_MAIN0570');
COMMIT;

注意事项：
1. 执行前建议备份数据库
2. 添加菜单后必须重启应用程序
3. 确保用户有访问新菜单的权限
4. 表名已修正为 MR_APP_ENTRY（不是 SEC_MENUS_DICT）
