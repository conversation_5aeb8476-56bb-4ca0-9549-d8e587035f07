using System;
using DevExpress.XtraEditors;
using Tjhis.Nurinp.Station.Service;

namespace Tjhis.Nurinp.Station.Tools
{
    /// <summary>
    /// 简单的菜单更新测试类
    /// </summary>
    public static class SimpleTest
    {
        /// <summary>
        /// 测试菜单更新功能
        /// </summary>
        public static void TestMenuUpdate()
        {
            try
            {
                // 执行菜单更新
                bool success = SimpleMenuUpdater.AddNursingRecordToMainMenu();
                
                if (success)
                {
                    // 验证结果
                    string result = SimpleMenuUpdater.ValidateMenu();
                    XtraMessageBox.Show($"菜单更新成功！\n\n{result}\n\n请重新启动应用程序查看效果。", 
                        "成功", System.Windows.Forms.MessageBoxButtons.OK, 
                        System.Windows.Forms.MessageBoxIcon.Information);
                }
                else
                {
                    XtraMessageBox.Show("菜单更新失败！", "失败", 
                        System.Windows.Forms.MessageBoxButtons.OK, 
                        System.Windows.Forms.MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"测试过程中发生异常：{ex.Message}", "异常", 
                    System.Windows.Forms.MessageBoxButtons.OK, 
                    System.Windows.Forms.MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 验证菜单状态
        /// </summary>
        public static void TestValidateMenu()
        {
            try
            {
                string result = SimpleMenuUpdater.ValidateMenu();
                XtraMessageBox.Show(result, "菜单状态验证", 
                    System.Windows.Forms.MessageBoxButtons.OK, 
                    System.Windows.Forms.MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"验证过程中发生异常：{ex.Message}", "异常", 
                    System.Windows.Forms.MessageBoxButtons.OK, 
                    System.Windows.Forms.MessageBoxIcon.Error);
            }
        }
    }
}
