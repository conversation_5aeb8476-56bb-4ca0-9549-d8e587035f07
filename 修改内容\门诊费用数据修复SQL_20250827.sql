-- ===============================================================================
-- 门诊医生站费用计算错误数据修复SQL脚本
-- 创建日期: 2025-08-27  
-- 说明: 修复CHARGES = COSTS * AMOUNT的错误计算模式
-- 执行顺序: 必须严格按照步骤顺序执行
-- ===============================================================================

-- ===============================================================================
-- 第一步: 数据备份（必须首先执行）
-- ===============================================================================

-- 1.1 备份费用明细表
CREATE TABLE OUTP_ORDERS_COSTS_BACKUP_20250827 AS 
SELECT * FROM OUTP_ORDERS_COSTS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30;

-- 记录备份行数
SELECT COUNT(*) AS "费用明细备份记录数" FROM OUTP_ORDERS_COSTS_BACKUP_20250827;

-- 1.2 备份医嘱主表
CREATE TABLE OUTP_ORDERS_STANDARD_BACKUP_20250827 AS 
SELECT * FROM OUTP_ORDERS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30;

-- 记录备份行数
SELECT COUNT(*) AS "医嘱主表备份记录数" FROM OUTP_ORDERS_STANDARD_BACKUP_20250827;

-- ===============================================================================
-- 第二步: 数据验证和分析（识别需要修复的记录）
-- ===============================================================================

-- 2.1 查询并验证错误数据模式（执行但不修改）
WITH ERROR_DATA AS (
    SELECT 
        PATIENT_ID, 
        VISIT_DATE,
        CLINIC_NO, 
        ORDER_NO, 
        ORDER_SUB_NO, 
        ITEM_NAME,
        ITEM_PRICE, 
        AMOUNT, 
        COSTS, 
        CHARGES,
        PRICE_QUOTIETY,
        -- 计算值
        ROUND(COSTS * AMOUNT, 4) AS COSTS_TIMES_AMOUNT,
        ROUND(ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1), 4) AS CORRECT_CHARGES,
        -- 判断条件
        CASE 
            WHEN ABS(CHARGES - COSTS * AMOUNT) < 0.01 
                 AND AMOUNT > 1 
                 AND CHARGES > (ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1) * 2)
            THEN 'Y' 
            ELSE 'N' 
        END AS NEED_FIX
    FROM OUTP_ORDERS_COSTS_STANDARD 
    WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30
      AND AMOUNT > 1  -- 排除数量为1的记录
)
SELECT 
    '错误记录统计' AS "类型",
    COUNT(*) AS "总记录数",
    COUNT(DISTINCT PATIENT_ID) AS "影响患者数",
    SUM(CHARGES - CORRECT_CHARGES) AS "多收费用总额",
    MIN(VISIT_DATE) AS "最早日期",
    MAX(VISIT_DATE) AS "最晚日期"
FROM ERROR_DATA 
WHERE NEED_FIX = 'Y';

-- 2.2 查看错误数据样本（前20条）
SELECT * FROM (
    SELECT 
        PATIENT_ID,
        ITEM_NAME AS "项目名称",
        ITEM_PRICE AS "单价",
        AMOUNT AS "数量", 
        COSTS AS "成本",
        CHARGES AS "当前收费",
        ROUND(ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1), 4) AS "正确收费",
        ROUND(CHARGES / (ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1)), 2) AS "错误倍数",
        VISIT_DATE AS "就诊日期"
    FROM OUTP_ORDERS_COSTS_STANDARD 
    WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30
      AND ABS(CHARGES - COSTS * AMOUNT) < 0.01  -- 错误模式
      AND AMOUNT > 1  -- 排除单个数量
      AND CHARGES > (ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1) * 2)  -- 明显错误
    ORDER BY VISIT_DATE DESC, CHARGES DESC
) WHERE ROWNUM <= 20;

-- ===============================================================================
-- 第三步: 生成修复清单（审核用）
-- ===============================================================================

-- 3.1 生成待修复记录清单（输出到临时表供审核）
CREATE TABLE OUTP_COSTS_FIX_LIST_20250827 AS
SELECT 
    PATIENT_ID,
    CLINIC_NO,
    ORDER_NO,
    ORDER_SUB_NO,
    ITEM_NAME,
    ITEM_PRICE,
    AMOUNT,
    COSTS,
    CHARGES AS OLD_CHARGES,
    ROUND(ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1), 4) AS NEW_CHARGES,
    CHARGES - ROUND(ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1), 4) AS DIFF_AMOUNT,
    VISIT_DATE,
    SYSDATE AS CREATE_TIME,
    'PENDING' AS FIX_STATUS
FROM OUTP_ORDERS_COSTS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30
  AND ABS(CHARGES - COSTS * AMOUNT) < 0.01  -- 错误计算模式
  AND AMOUNT > 1  -- 排除数量为1
  AND CHARGES > (ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1) * 2);  -- 收费明显过高

-- 查看修复清单统计
SELECT 
    COUNT(*) AS "待修复记录数",
    COUNT(DISTINCT PATIENT_ID) AS "影响患者数",
    SUM(DIFF_AMOUNT) AS "需退费总额",
    MIN(VISIT_DATE) AS "最早日期",
    MAX(VISIT_DATE) AS "最晚日期"
FROM OUTP_COSTS_FIX_LIST_20250827;

-- ===============================================================================
-- 第四步: 执行数据修复（需要人工确认后执行）
-- ===============================================================================

-- 4.1 修复费用明细表中的CHARGES错误
-- 注意：请在确认修复清单无误后再执行此步骤！
UPDATE OUTP_ORDERS_COSTS_STANDARD C
SET CHARGES = (
    SELECT NEW_CHARGES 
    FROM OUTP_COSTS_FIX_LIST_20250827 F
    WHERE F.CLINIC_NO = C.CLINIC_NO 
      AND F.ORDER_NO = C.ORDER_NO 
      AND F.ORDER_SUB_NO = C.ORDER_SUB_NO
      AND F.FIX_STATUS = 'PENDING'
),
LAST_UPDATE_DATE = SYSDATE,
UPDATE_BY = 'FIX_20250827'
WHERE EXISTS (
    SELECT 1 
    FROM OUTP_COSTS_FIX_LIST_20250827 F
    WHERE F.CLINIC_NO = C.CLINIC_NO 
      AND F.ORDER_NO = C.ORDER_NO 
      AND F.ORDER_SUB_NO = C.ORDER_SUB_NO
      AND F.FIX_STATUS = 'PENDING'
);

-- 记录修复的行数
SELECT SQL%ROWCOUNT AS "费用明细修复记录数" FROM DUAL;

-- 4.2 更新修复状态
UPDATE OUTP_COSTS_FIX_LIST_20250827
SET FIX_STATUS = 'FIXED',
    FIX_TIME = SYSDATE
WHERE FIX_STATUS = 'PENDING';

COMMIT;

-- 4.3 更新医嘱主表的费用汇总
UPDATE OUTP_ORDERS_STANDARD O
SET (COSTS, CHARGES) = (
    SELECT 
        NVL(SUM(C.COSTS), 0), 
        NVL(SUM(C.CHARGES), 0)
    FROM OUTP_ORDERS_COSTS_STANDARD C
    WHERE C.CLINIC_NO = O.CLINIC_NO
      AND C.ORDER_NO = O.ORDER_NO
      AND C.ORDER_SUB_NO = O.ORDER_SUB_NO
),
LAST_UPDATE_DATE = SYSDATE,
UPDATE_BY = 'FIX_20250827'
WHERE EXISTS (
    SELECT 1
    FROM OUTP_COSTS_FIX_LIST_20250827 F
    WHERE F.CLINIC_NO = O.CLINIC_NO
      AND F.ORDER_NO = O.ORDER_NO
      AND F.FIX_STATUS = 'FIXED'
);

-- 记录修复的行数
SELECT SQL%ROWCOUNT AS "医嘱主表修复记录数" FROM DUAL;

COMMIT;

-- ===============================================================================
-- 第五步: 验证修复结果
-- ===============================================================================

-- 5.1 验证是否还有错误记录
SELECT COUNT(*) AS "仍存在的错误记录数"
FROM OUTP_ORDERS_COSTS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30
  AND ABS(CHARGES - COSTS * AMOUNT) < 0.01
  AND AMOUNT > 1
  AND CHARGES > (ITEM_PRICE * AMOUNT * NVL(PRICE_QUOTIETY, 1) * 2);

-- 5.2 验证费用汇总一致性
WITH SUMMARY_CHECK AS (
    SELECT 
        O.CLINIC_NO,
        O.COSTS AS MAIN_COSTS,
        O.CHARGES AS MAIN_CHARGES,
        SUM(C.COSTS) AS DETAIL_COSTS_SUM,
        SUM(C.CHARGES) AS DETAIL_CHARGES_SUM,
        ABS(O.COSTS - SUM(C.COSTS)) AS COSTS_DIFF,
        ABS(O.CHARGES - SUM(C.CHARGES)) AS CHARGES_DIFF
    FROM OUTP_ORDERS_STANDARD O
    JOIN OUTP_ORDERS_COSTS_STANDARD C 
        ON O.CLINIC_NO = C.CLINIC_NO 
        AND O.ORDER_NO = C.ORDER_NO 
        AND O.ORDER_SUB_NO = C.ORDER_SUB_NO
    WHERE O.VISIT_DATE >= TRUNC(SYSDATE) - 7
    GROUP BY O.CLINIC_NO, O.COSTS, O.CHARGES
)
SELECT 
    '费用汇总一致性检查' AS "检查项",
    COUNT(*) AS "不一致记录数",
    MAX(COSTS_DIFF) AS "最大COSTS差异",
    MAX(CHARGES_DIFF) AS "最大CHARGES差异"
FROM SUMMARY_CHECK
WHERE COSTS_DIFF > 0.01 OR CHARGES_DIFF > 0.01;

-- 5.3 对比修复前后的数据
SELECT 
    '修复效果对比' AS "统计项",
    COUNT(*) AS "修复记录数",
    SUM(OLD_CHARGES) AS "修复前总收费",
    SUM(NEW_CHARGES) AS "修复后总收费", 
    SUM(DIFF_AMOUNT) AS "退费总额",
    AVG(DIFF_AMOUNT) AS "平均退费额"
FROM OUTP_COSTS_FIX_LIST_20250827
WHERE FIX_STATUS = 'FIXED';

-- ===============================================================================
-- 第六步: 生成退费清单（财务用）
-- ===============================================================================

-- 6.1 生成患者退费汇总
CREATE TABLE PATIENT_REFUND_LIST_20250827 AS
SELECT 
    PATIENT_ID,
    COUNT(*) AS ITEM_COUNT,
    SUM(DIFF_AMOUNT) AS REFUND_AMOUNT,
    MIN(VISIT_DATE) AS FIRST_VISIT,
    MAX(VISIT_DATE) AS LAST_VISIT,
    SYSDATE AS CREATE_TIME
FROM OUTP_COSTS_FIX_LIST_20250827
WHERE FIX_STATUS = 'FIXED'
  AND DIFF_AMOUNT > 0
GROUP BY PATIENT_ID
ORDER BY REFUND_AMOUNT DESC;

-- 查看退费清单
SELECT * FROM PATIENT_REFUND_LIST_20250827 
WHERE REFUND_AMOUNT > 100  -- 只显示退费金额大于100元的
ORDER BY REFUND_AMOUNT DESC;

-- ===============================================================================
-- 第七步: 清理和归档
-- ===============================================================================

-- 7.1 创建归档表（保留修复记录30天）
CREATE TABLE OUTP_COSTS_FIX_ARCHIVE AS
SELECT * FROM OUTP_COSTS_FIX_LIST_20250827;

-- 7.2 添加索引便于查询
CREATE INDEX IDX_FIX_ARCHIVE_PATIENT ON OUTP_COSTS_FIX_ARCHIVE(PATIENT_ID);
CREATE INDEX IDX_FIX_ARCHIVE_DATE ON OUTP_COSTS_FIX_ARCHIVE(VISIT_DATE);

-- ===============================================================================
-- 回滚脚本（紧急情况下使用）
-- ===============================================================================

-- 如需回滚，请执行以下SQL：
/*
-- 恢复费用明细表
UPDATE OUTP_ORDERS_COSTS_STANDARD C
SET (COSTS, CHARGES) = (
    SELECT B.COSTS, B.CHARGES
    FROM OUTP_ORDERS_COSTS_BACKUP_20250827 B
    WHERE B.CLINIC_NO = C.CLINIC_NO
      AND B.ORDER_NO = C.ORDER_NO
      AND B.ORDER_SUB_NO = C.ORDER_SUB_NO
)
WHERE EXISTS (
    SELECT 1
    FROM OUTP_ORDERS_COSTS_BACKUP_20250827 B
    WHERE B.CLINIC_NO = C.CLINIC_NO
      AND B.ORDER_NO = C.ORDER_NO
);

-- 恢复医嘱主表
UPDATE OUTP_ORDERS_STANDARD O
SET (COSTS, CHARGES) = (
    SELECT B.COSTS, B.CHARGES
    FROM OUTP_ORDERS_STANDARD_BACKUP_20250827 B
    WHERE B.CLINIC_NO = O.CLINIC_NO
      AND B.ORDER_NO = O.ORDER_NO
)
WHERE EXISTS (
    SELECT 1
    FROM OUTP_ORDERS_STANDARD_BACKUP_20250827 B
    WHERE B.CLINIC_NO = O.CLINIC_NO
      AND B.ORDER_NO = O.ORDER_NO
);

COMMIT;
*/

-- ===============================================================================
-- 脚本结束 - 请按顺序执行并验证每一步结果
-- ===============================================================================