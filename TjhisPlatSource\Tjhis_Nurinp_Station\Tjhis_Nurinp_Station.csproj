<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <SSDTUnitTestPath Condition="'$(SSDTUnitTestPath)' == ''">$(VsInstallRoot)\Common7\IDE\Extensions\Microsoft\SQLDB</SSDTUnitTestPath>
  </PropertyGroup>
  <PropertyGroup>
    <SSDTPath Condition="'$(SSDTPath)' == ''">$(VsInstallRoot)\Common7\IDE\Extensions\Microsoft\SQLDB\DAC\130</SSDTPath>
  </PropertyGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
  </PropertyGroup>
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{C32B3933-8C63-4209-8444-A56BE12D9370}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>Tjhis.Nurinp.Station</RootNamespace>
    <AssemblyName>Tjhis.Nurinp.Station</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>..\TJHisPlatEXE\Client\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject />
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="AxInterop.EMRPAD30Lib, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\EMRPad30lib\AxInterop.EMRPAD30Lib.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Charts.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.Charts.v19.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Data.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.Data.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Images.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.Images.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Pdf.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.Pdf.v19.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.Printing.v19.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v19.1.Core, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.RichEdit.v19.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.Utils.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.PdfViewer.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.Xpf.PdfViewer.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraBars.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraCharts.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraCharts.v19.1.UI, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraCharts.v19.1.UI.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraDialogs.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraDialogs.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraEditors.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraGrid.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraLayout.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPdfViewer.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraPdfViewer.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraPrinting.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraPrinting.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraReports.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraReports.v19.1.Extensions, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraReports.v19.1.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraRichEdit.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraTreeList.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v19.1, Version=19.1.7.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DevExpress.XtraVerticalGrid.v19.1.dll</HintPath>
    </Reference>
    <Reference Include="DotNetBarcode, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\DotNetBarcode.dll</HintPath>
    </Reference>
    <Reference Include="INMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\INMService.dll</HintPath>
    </Reference>
    <Reference Include="Model, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Model.dll</HintPath>
    </Reference>
    <Reference Include="NMService, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\NMService.dll</HintPath>
    </Reference>
    <Reference Include="PlatCommon, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\PlatCommon.dll</HintPath>
    </Reference>
    <Reference Include="PlatCommonForm, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\PlatCommonForm.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.Management" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Formatters.Soap" />
    <Reference Include="System.Security" />
    <Reference Include="System.Speech" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="Tjhis.ClinicalPath.Station, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Tjhis.ClinicalPath.Station.dll</HintPath>
    </Reference>
    <Reference Include="Tjhis.Doctor.Station, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Tjhis.Doctor.Station.dll</HintPath>
    </Reference>
    <Reference Include="Tjhis.EmrManager.Common, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Tjhis.EmrManager.Common.dll</HintPath>
    </Reference>
    <Reference Include="Tjhis.Interface.CA, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Tjhis.Interface.CA.dll</HintPath>
    </Reference>
    <Reference Include="Tjhis.Interface.CDSS, Version=1.0.0.0, Culture=neutral, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Tjhis.Interface.CDSS.dll</HintPath>
    </Reference>
    <Reference Include="Tjhis.Interface.Station, Version=6.6.4.3, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Tjhis.Interface.Station.dll</HintPath>
    </Reference>
    <Reference Include="Tjhis_Mq_Service, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Tjhis_Mq_Service.dll</HintPath>
    </Reference>
    <Reference Include="TJTextEditor, Version=2.2.4.22, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\TJTextEditor.dll</HintPath>
    </Reference>
    <Reference Include="Utility, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\TJHisPlatEXE\Client\Utility.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ADT\frmAdtLog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmAdtLog.Designer.cs">
      <DependentUpon>frmAdtLog.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmBedExchange.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmBedExchange.Designer.cs">
      <DependentUpon>frmBedExchange.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmBedSideCard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmBedSideCard.Designer.cs">
      <DependentUpon>frmBedSideCard.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmBedsideCardWristbandPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmBedsideCardWristbandPrint.Designer.cs">
      <DependentUpon>frmBedsideCardWristbandPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmBedsideMultiPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmBedsideMultiPrint.designer.cs">
      <DependentUpon>frmBedsideMultiPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmCancelDischarge.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmCancelDischarge.Designer.cs">
      <DependentUpon>frmCancelDischarge.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmCancelPatientIn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmCancelPatientIn.Designer.cs">
      <DependentUpon>frmCancelPatientIn.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmCancelTransfer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmCancelTransfer.Designer.cs">
      <DependentUpon>frmCancelTransfer.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmContractBed.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmContractBed.Designer.cs">
      <DependentUpon>frmContractBed.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmContractRoom.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmContractRoom.Designer.cs">
      <DependentUpon>frmContractRoom.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmDailyLog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmDailyLog.Designer.cs">
      <DependentUpon>frmDailyLog.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmDisChargeProc.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmDisChargeProc.Designer.cs">
      <DependentUpon>frmDisChargeProc.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmNewbornDetail.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmNewbornDetail.Designer.cs">
      <DependentUpon>frmNewbornDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\FrmNewBornEval.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmNewBornEval.Designer.cs">
      <DependentUpon>FrmNewBornEval.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmNewbornIn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmNewbornIn.Designer.cs">
      <DependentUpon>frmNewbornIn.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\FrmNewBornInput.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmNewBornInput.Designer.cs">
      <DependentUpon>FrmNewBornInput.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\FrmNewBornList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmNewBornList.Designer.cs">
      <DependentUpon>FrmNewBornList.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmDisChargeCheck.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmDisChargeCheck.Designer.cs">
      <DependentUpon>frmDisChargeCheck.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmPatientIn.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmPatientIn.Designer.cs">
      <DependentUpon>frmPatientIn.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmPatientInfoModify.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmPatientInfoModify.Designer.cs">
      <DependentUpon>FrmPatientInfoModify.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\FrmPatOutHospital.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmPatOutHospital.Designer.cs">
      <DependentUpon>FrmPatOutHospital.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmPreOut.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmPreOut.Designer.cs">
      <DependentUpon>frmPreOut.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\FrmSelectCencelFlag.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmSelectCencelFlag.Designer.cs">
      <DependentUpon>FrmSelectCencelFlag.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmSetThreeLevelDoctors.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\frmSetThreeLevelDoctors.Designer.cs">
      <DependentUpon>frmSetThreeLevelDoctors.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\frmTransferProc.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\FrmTransferProc.designer.cs">
      <DependentUpon>frmTransferProc.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\MainFrm2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\MainFrm2.Designer.cs">
      <DependentUpon>MainFrm2.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\OrderMsg.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\OrderMsg.Designer.cs">
      <DependentUpon>OrderMsg.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\rptBirthCertificate.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ADT\rptBirthCertificate.Designer.cs">
      <DependentUpon>rptBirthCertificate.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\TextInputFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\TextInputFrm.Designer.cs">
      <DependentUpon>TextInputFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\WardOverViewFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\WardOverViewFrm.Designer.cs">
      <DependentUpon>WardOverViewFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="ADT\WristBandTypeSelectFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ADT\WristBandTypeSelectFrm.Designer.cs">
      <DependentUpon>WristBandTypeSelectFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmBillPatternCall.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmBillPatternCall.designer.cs">
      <DependentUpon>frmBillPatternCall.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmDischargedRefundHdpart.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmDischargedRefundHdpart.Designer.cs">
      <DependentUpon>frmDischargedRefundHdpart.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmDrawFinancial.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmDrawFinancial.designer.cs">
      <DependentUpon>frmDrawFinancial.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmGetPrepayments.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmGetPrepayments.designer.cs">
      <DependentUpon>frmGetPrepayments.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmHighQuality.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmHighQuality.designer.cs">
      <DependentUpon>frmHighQuality.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmHighQualityRetun.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmHighQualityRetun.designer.cs">
      <DependentUpon>frmHighQualityRetun.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmInputsetting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmInputSetting.designer.cs">
      <DependentUpon>frmInputsetting.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmMuchPatientItem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmMuchPatientItem.designer.cs">
      <DependentUpon>frmMuchPatientItem.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmNoRecipeNoCheck.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmNoRecipeNoCheck.designer.cs">
      <DependentUpon>frmNoRecipeNoCheck.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmOrderCostVerify.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmOrderCostVerify.designer.cs">
      <DependentUpon>frmOrderCostVerify.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmOrderCostVerifyOut.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmOrderCostVerifyOut.designer.cs">
      <DependentUpon>frmOrderCostVerifyOut.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmOrderCostVerifyOutPat.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmOrderCostVerifyOutPat.designer.cs">
      <DependentUpon>frmOrderCostVerifyOutPat.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmPatBillMiscItemPatch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmPatBillMiscItemPatch.designer.cs">
      <DependentUpon>frmPatBillMiscItemPatch.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmSetDate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmSetDate.designer.cs">
      <DependentUpon>frmSetDate.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\frmValidateUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Bill\frmValidateUser.designer.cs">
      <DependentUpon>frmValidateUser.cs</DependentUpon>
    </Compile>
    <Compile Include="Bill\valuation_detail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Bill\valuation_detail.designer.cs">
      <DependentUpon>valuation_detail.cs</DependentUpon>
    </Compile>
    <Compile Include="CDSS\CdssExt.cs" />
    <Compile Include="Material\frmDispenseReq.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmDispenseReq.designer.cs">
      <DependentUpon>frmDispenseReq.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmExApplication.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmExApplication.designer.cs">
      <DependentUpon>frmExApplication.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmHighQuality.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmHighQuality.designer.cs">
      <DependentUpon>frmHighQuality.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmHighQualityRetun.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmHighQualityRetun.designer.cs">
      <DependentUpon>frmHighQualityRetun.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\FrmInpSettleCancelCheck.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\FrmInpSettleCancelCheck.Designer.cs">
      <DependentUpon>FrmInpSettleCancelCheck.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\FrmMedcostQuery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\FrmMedcostQuery.Designer.cs">
      <DependentUpon>FrmMedcostQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\FrmMedcostQueryPatientList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\FrmMedcostQueryPatientList.Designer.cs">
      <DependentUpon>FrmMedcostQueryPatientList.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmPpcOrderPre.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmPpcOrderPre.designer.cs">
      <DependentUpon>frmPpcOrderPre.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmPrescHandBack.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmPrescHandBack.Designer.cs">
      <DependentUpon>frmPrescHandBack.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmPrescHandBackByDisp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmPrescHandBackByDisp.designer.cs">
      <DependentUpon>frmPrescHandBackByDisp.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\FrmPrescHandbackHistory.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\FrmPrescHandbackHistory.Designer.cs">
      <DependentUpon>FrmPrescHandbackHistory.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmprescHandBackQuery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmprescHandBackQuery.designer.cs">
      <DependentUpon>frmprescHandBackQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmPrescQueryList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmPrescQueryList.designer.cs">
      <DependentUpon>frmPrescQueryList.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frmwApplicationNew.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frmwApplicationNew.designer.cs">
      <DependentUpon>frmwApplicationNew.cs</DependentUpon>
    </Compile>
    <Compile Include="Material\frPatsInHospital.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Material\frPatsInHospital.designer.cs">
      <DependentUpon>frPatsInHospital.cs</DependentUpon>
    </Compile>
    <Compile Include="NuradmRule\AppHotKey.cs" />
    <Compile Include="NuradmRule\frmPDFViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NuradmRule\frmPDFViewer.Designer.cs">
      <DependentUpon>frmPDFViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="NuradmRule\frmRulePersonInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NuradmRule\frmRulePersonInfo.Designer.cs">
      <DependentUpon>frmRulePersonInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="NuradmRule\frmWordViewer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NuradmRule\frmWordViewer.Designer.cs">
      <DependentUpon>frmWordViewer.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\EmrEditorDusignPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="NurRecSetting\FormAddParam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\FormAddParam.designer.cs">
      <DependentUpon>FormAddParam.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\FormMacroTarget.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\FormMacroTarget.designer.cs">
      <DependentUpon>FormMacroTarget.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\FormPDACataLog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\FormPDACataLog.designer.cs">
      <DependentUpon>FormPDACataLog.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmKnowledgeDept.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmKnowledgeDept.Designer.cs">
      <DependentUpon>frmKnowledgeDept.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmKnowledgeStand.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmKnowledgeStand.Designer.cs">
      <DependentUpon>frmKnowledgeStand.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictClass.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictClass.designer.cs">
      <DependentUpon>frmNrDictClass.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictEduBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictEduBase.designer.cs">
      <DependentUpon>frmNrDictEduBase.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictEduItem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictEduItem.designer.cs">
      <DependentUpon>frmNrDictEduItem.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictInspection.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictInspection.designer.cs">
      <DependentUpon>frmNrDictInspection.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictMacro.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictMacro.designer.cs">
      <DependentUpon>frmNrDictMacro.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictPlanBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictPlanBase.designer.cs">
      <DependentUpon>frmNrDictPlanBase.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictPlanTemplet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictPlanTemplet.designer.cs">
      <DependentUpon>frmNrDictPlanTemplet.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictTransReason.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNrDictTransReason.designer.cs">
      <DependentUpon>frmNrDictTransReason.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNRRecordItem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNRRecordItem.Designer.cs">
      <DependentUpon>frmNRRecordItem.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNRSubsetPDACATALOG.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNRSubsetPDACATALOG.Designer.cs">
      <DependentUpon>frmNRSubsetPDACATALOG.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRecSetting\frmNRSubsetVSClass.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRecSetting\frmNRSubsetVSClass.designer.cs">
      <DependentUpon>frmNRSubsetVSClass.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\DlgLineSpacingSetting.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\DlgLineSpacingSetting.designer.cs">
      <DependentUpon>DlgLineSpacingSetting.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\DocEmrUC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NurRec\DocEmrUC.Designer.cs">
      <DependentUpon>DocEmrUC.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\DocRenameFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\DocRenameFrm.Designer.cs">
      <DependentUpon>DocRenameFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\EMRDocToolBaseFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\EMRDocToolBaseFrm.Designer.cs">
      <DependentUpon>EMRDocToolBaseFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\eSingnature\AuxiliaryDictionary.cs" />
    <Compile Include="NurRec\eSingnature\CaAncestor.cs" />
    <Compile Include="NurRec\eSingnature\ICaAncestor.cs" />
    <Compile Include="NurRec\FileExitWarningDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FileExitWarningDialog.Designer.cs">
      <DependentUpon>FileExitWarningDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FileTrans.cs" />
    <Compile Include="NurRec\Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FormNRDocTempletFileAdd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FormNRDocTempletFileAdd.Designer.cs">
      <DependentUpon>FormNRDocTempletFileAdd.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FormNRDocTempletSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FormNRDocTempletSelect.Designer.cs">
      <DependentUpon>FormNRDocTempletSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FormPDACataLog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FormPDACataLog.designer.cs">
      <DependentUpon>FormPDACataLog.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FormPDAConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FormPDAConfig.designer.cs">
      <DependentUpon>FormPDAConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FormSqlError.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FormSqlError.Designer.cs">
      <DependentUpon>FormSqlError.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmAddHealthEduDiag.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmAddHealthEduDiag.designer.cs">
      <DependentUpon>frmAddHealthEduDiag.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmAddPlanRec.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmAddPlanRec.designer.cs">
      <DependentUpon>frmAddPlanRec.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmAddPoctObserve.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmAddPoctObserve.designer.cs">
      <DependentUpon>frmAddPoctObserve.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmAddPoctRecord.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmAddPoctRecord.designer.cs">
      <DependentUpon>frmAddPoctRecord.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmAddRoundRec.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmAddRoundRec.designer.cs">
      <DependentUpon>frmAddRoundRec.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmAddStructedElement.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmAddStructedElement.Designer.cs">
      <DependentUpon>frmAddStructedElement.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmBrowsemr.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmBrowsemr.Designer.cs">
      <DependentUpon>frmBrowsemr.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmBrowseOrders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmBrowseOrders.designer.cs">
      <DependentUpon>frmBrowseOrders.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmBrowsExam.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmBrowsExam.Designer.cs">
      <DependentUpon>frmBrowsExam.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmBrowsLab.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmBrowsLab.Designer.cs">
      <DependentUpon>frmBrowsLab.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmBrowsTem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmBrowstem.Designer.cs">
      <DependentUpon>frmBrowsTem.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmBrowsTemp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmBrowsTemp.Designer.cs">
      <DependentUpon>frmBrowsTemp.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmCatheter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmCatheter.Designer.cs">
      <DependentUpon>frmCatheter.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmConsultSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmConsultSearch.designer.cs">
      <DependentUpon>frmConsultSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmDocEMRShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmDocEMRShow.Designer.cs">
      <DependentUpon>frmDocEMRShow.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmDocRename.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmDocRename.Designer.cs">
      <DependentUpon>frmDocRename.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmDrugsPoisonHemp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmDrugsPoisonHemp.Designer.cs">
      <DependentUpon>frmDrugsPoisonHemp.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmEduItemTypeContentSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmEduItemTypeContentSet.designer.cs">
      <DependentUpon>frmEduItemTypeContentSet.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmFIleshow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmFIleshow.designer.cs">
      <DependentUpon>frmFIleshow.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmGoodsMaintain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmGoodsMaintain.Designer.cs">
      <DependentUpon>frmGoodsMaintain.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmHealthEdu.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmHealthEdu.designer.cs">
      <DependentUpon>frmHealthEdu.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmHealthEduItemSelector.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmHealthEduItemSelector.designer.cs">
      <DependentUpon>frmHealthEduItemSelector.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmHierarchyTree.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmHierarchyTree.designer.cs">
      <DependentUpon>frmHierarchyTree.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmItemsRecorded.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmItemsRecorded.Designer.cs">
      <DependentUpon>frmItemsRecorded.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNRCount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNRCount.designer.cs">
      <DependentUpon>frmNRCount.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNRDocTempletChangeTitle.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNRDocTempletChangeTitle.Designer.cs">
      <DependentUpon>frmNRDocTempletChangeTitle.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNRDocTempletFile.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNRDocTempletFile.Designer.cs">
      <DependentUpon>frmNRDocTempletFile.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNRDocTempletSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNRDocTempletSearch.Designer.cs">
      <DependentUpon>frmNRDocTempletSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNREvaluatePain.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNREvaluatePain.designer.cs">
      <DependentUpon>frmNREvaluatePain.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNREvaluatePain_DarwFigure.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNREvaluatePain_DarwFigure.designer.cs">
      <DependentUpon>frmNREvaluatePain_DarwFigure.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNrGutter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNrGutter.designer.cs">
      <DependentUpon>frmNrGutter.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNrRecordSearch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNrRecordSearch.designer.cs">
      <DependentUpon>frmNrRecordSearch.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNurRecUnLock.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNurRecUnLock.designer.cs">
      <DependentUpon>frmNurRecUnLock.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNursingConsult.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNursingConsult.designer.cs">
      <DependentUpon>frmNursingConsult.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNursingDoc.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNursingDoc.Designer.cs">
      <DependentUpon>frmNursingDoc.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNursingPlan.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNursingPlan.designer.cs">
      <DependentUpon>frmNursingPlan.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmNursingRound.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmNursingRound.designer.cs">
      <DependentUpon>frmNursingRound.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmOldDocEMRShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmOldDocEMRShow.Designer.cs">
      <DependentUpon>frmOldDocEMRShow.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmOperation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmOperation.Designer.cs">
      <DependentUpon>frmOperation.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FrmPatientEMRCenter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FrmPatientEMRCenter.Designer.cs">
      <DependentUpon>FrmPatientEMRCenter.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FrmPatList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FrmPatList.Designer.cs">
      <DependentUpon>FrmPatList.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmPatNurseFollowManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmPatNurseFollowManager.Designer.cs">
      <DependentUpon>frmPatNurseFollowManager.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmPatSelector.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmPatSelector.Designer.cs">
      <DependentUpon>frmPatSelector.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmPlanEva.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmPlanEva.designer.cs">
      <DependentUpon>frmPlanEva.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmPoctRecord.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmPoctRecord.designer.cs">
      <DependentUpon>frmPoctRecord.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmSectionName.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmSectionName.designer.cs">
      <DependentUpon>frmSectionName.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmSelectNRDocTemplete.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmSelectNRDocTemplete.Designer.cs">
      <DependentUpon>frmSelectNRDocTemplete.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmSelectPatientHeat.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmSelectPatientHeat.designer.cs">
      <DependentUpon>frmSelectPatientHeat.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmShiftWork.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmShiftWork.Designer.cs">
      <DependentUpon>frmShiftWork.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmSignsTypeMessage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmSignsTypeMessage.Designer.cs">
      <DependentUpon>frmSignsTypeMessage.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmSuccessionIsSet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmSuccessionIsSet.designer.cs">
      <DependentUpon>frmSuccessionIsSet.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FrmTempSign.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\FrmTempSign.Designer.cs">
      <DependentUpon>FrmTempSign.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\frmToDoList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\frmToDoList.Designer.cs">
      <DependentUpon>frmToDoList.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\FtpUpDown.cs" />
    <Compile Include="NurRec\ImportOrdersDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\ImportOrdersDialog.Designer.cs">
      <DependentUpon>ImportOrdersDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\InOutSumColfrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\InOutSumColfrm.Designer.cs">
      <DependentUpon>InOutSumColfrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\InOutSumDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\InOutSumDialog.Designer.cs">
      <DependentUpon>InOutSumDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\InOutTempfrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\InOutTempfrm.Designer.cs">
      <DependentUpon>InOutTempfrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\InpPatSelectFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\InpPatSelectFrm.Designer.cs">
      <DependentUpon>InpPatSelectFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\NurEmrTempletFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\NurEmrTempletFrm.Designer.cs">
      <DependentUpon>NurEmrTempletFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\NurEmrUC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NurRec\NurEmrUC.Designer.cs">
      <DependentUpon>NurEmrUC.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\NurEmrVersionListFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\NurEmrVersionListFrm.Designer.cs">
      <DependentUpon>NurEmrVersionListFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\RegularTextFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\RegularTextFrm.Designer.cs">
      <DependentUpon>RegularTextFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\SelMarcroListFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\SelMarcroListFrm.Designer.cs">
      <DependentUpon>SelMarcroListFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\ToothExpInput.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\ToothExpInput.Designer.cs">
      <DependentUpon>ToothExpInput.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\ucNRCount.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NurRec\ucNRCount.designer.cs">
      <DependentUpon>ucNRCount.cs</DependentUpon>
    </Compile>
    <Compile Include="NurRec\UpdMarcroListFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NurRec\UpdMarcroListFrm.Designer.cs">
      <DependentUpon>UpdMarcroListFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\DictManagerFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\DictManagerFrm.Designer.cs">
      <DependentUpon>DictManagerFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\DictSelectorFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\DictSelectorFrm.Designer.cs">
      <DependentUpon>DictSelectorFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\DocEmrUC.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\DocEmrUC.Designer.cs">
      <DependentUpon>DocEmrUC.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\Entities\DataType.cs" />
    <Compile Include="NursingRecordSheet\Entities\ImportEntity.cs" />
    <Compile Include="NursingRecordSheet\Entities\IniFile.cs" />
    <Compile Include="NursingRecordSheet\Entities\KnowledgeEntity.cs" />
    <Compile Include="NursingRecordSheet\Entities\MDataGridViewCalendarColumn.cs" />
    <Compile Include="NursingRecordSheet\Entities\PatientEventArgs.cs" />
    <Compile Include="NursingRecordSheet\Entities\PrinterInfo.cs" />
    <Compile Include="NursingRecordSheet\Entities\PrintTool.cs" />
    <Compile Include="NursingRecordSheet\Entities\ZebraPrinter.cs" />
    <Compile Include="NursingRecordSheet\frmDataQuery.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmDataQuery.Designer.cs">
      <DependentUpon>frmDataQuery.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmDocEMRShow.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmDocEMRShow.Designer.cs">
      <DependentUpon>frmDocEMRShow.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmFood.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmFood.Designer.cs">
      <DependentUpon>frmFood.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmInOutCount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmInOutCount.Designer.cs">
      <DependentUpon>frmInOutCount.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmLineChart.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmLineChart.Designer.cs">
      <DependentUpon>frmLineChart.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmNursingRecord.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmNursingRecord.Designer.cs">
      <DependentUpon>frmNursingRecord.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\FrmOrders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\FrmOrders.designer.cs">
      <DependentUpon>FrmOrders.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmPrintView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmPrintView.Designer.cs">
      <DependentUpon>frmPrintView.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmRecordBatInp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmRecordBatInp.designer.cs">
      <DependentUpon>frmRecordBatInp.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmRecordBatInp_Data.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmRecordDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmRecordDesigner.Designer.cs">
      <DependentUpon>frmRecordDesigner.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmRecordView.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmRecordView.Designer.cs">
      <DependentUpon>frmRecordView.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmSevereRecord.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmSevereRecord.Designer.cs">
      <DependentUpon>frmSevereRecord.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\frmSum.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\frmSum.Designer.cs">
      <DependentUpon>frmSum.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\FrmVital.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\FrmVital.designer.cs">
      <DependentUpon>FrmVital.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\InOutTempForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\InOutTempForm.Designer.cs">
      <DependentUpon>InOutTempForm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\OptionSelectionFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\OptionSelectionFrm.designer.cs">
      <DependentUpon>OptionSelectionFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientList.Designer.cs">
      <DependentUpon>PatientList.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientListWardFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientListWardFrm.designer.cs">
      <DependentUpon>PatientListWardFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientOut.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientOut.designer.cs">
      <DependentUpon>PatientOut.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientSearchFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\PatientSearchFrm.designer.cs">
      <DependentUpon>PatientSearchFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\PrintModeSelectFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\PrintModeSelectFrm.designer.cs">
      <DependentUpon>PrintModeSelectFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\RadioValueSelectFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\RadioValueSelectFrm.designer.cs">
      <DependentUpon>RadioValueSelectFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\RassScore.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\RassScore.Designer.cs">
      <DependentUpon>RassScore.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\ScoreDesignForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\ScoreDesignForm.Designer.cs">
      <DependentUpon>ScoreDesignForm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\ScoreEvaluate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\ScoreEvaluate.Designer.cs">
      <DependentUpon>ScoreEvaluate.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\TemplateViewFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NursingRecordSheet\TemplateViewFrm.designer.cs">
      <DependentUpon>TemplateViewFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="NursingRecordSheet\TJGridViewComboColumn.cs" />
    <Compile Include="Orders\frmBrowseOrders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmBrowseOrders.designer.cs">
      <DependentUpon>frmBrowseOrders.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmBrowseOrdersError.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmBrowseOrdersError.designer.cs">
      <DependentUpon>frmBrowseOrdersError.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmCancelOrderAudit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmCancelOrderAudit.Designer.cs">
      <DependentUpon>frmCancelOrderAudit.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmDictFeeItem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmDictFeeItem.Designer.cs">
      <DependentUpon>frmDictFeeItem.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrderAudit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrderAudit.designer.cs">
      <DependentUpon>frmOrderAudit.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrderAuditWard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrderAuditWard.designer.cs">
      <DependentUpon>frmOrderAuditWard.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrderCosts.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrderCosts.designer.cs">
      <DependentUpon>frmOrderCosts.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrderPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrderPrint.designer.cs">
      <DependentUpon>frmOrderPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrderPrintOption.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrderPrintOption.designer.cs">
      <DependentUpon>frmOrderPrintOption.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrdersBook.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrdersBook.designer.cs">
      <DependentUpon>frmOrdersBook.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrdersExecute.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrdersExecute.designer.cs">
      <DependentUpon>frmOrdersExecute.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrdersExecuteMemo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrdersExecuteMemo.designer.cs">
      <DependentUpon>frmOrdersExecuteMemo.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrdersSplitPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrdersSplitPrint.designer.cs">
      <DependentUpon>frmOrdersSplitPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrdersTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrdersTest.Designer.cs">
      <DependentUpon>frmOrdersTest.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrderStopDate.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrderStopDate.Designer.cs">
      <DependentUpon>frmOrderStopDate.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmOrderVerifyList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmOrderVerifyList.designer.cs">
      <DependentUpon>frmOrderVerifyList.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmPrintOrderBook.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmPrintOrderBook.designer.cs">
      <DependentUpon>frmPrintOrderBook.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmSearchOrder.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmSearchOrder.designer.cs">
      <DependentUpon>frmSearchOrder.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmStaffSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmStaffSelect.designer.cs">
      <DependentUpon>frmStaffSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmTestItemDetail.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmTestItemDetail.designer.cs">
      <DependentUpon>frmTestItemDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\frmTestsPecimenGather.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\frmTestsPecimenGather.designer.cs">
      <DependentUpon>frmTestsPecimenGather.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\OrderSplitTestFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\OrderSplitTestFrm.Designer.cs">
      <DependentUpon>OrderSplitTestFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\TestSpeciGatherFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\TestSpeciGatherFrm.Designer.cs">
      <DependentUpon>TestSpeciGatherFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Orders\TestsPecimenGatherFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Orders\TestsPecimenGatherFrm.designer.cs">
      <DependentUpon>TestsPecimenGatherFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="PatientInfo\EmrEditorControlDev.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="PatientInfo\EmrEditorControlDev.Designer.cs">
      <DependentUpon>EmrEditorControlDev.cs</DependentUpon>
    </Compile>
    <Compile Include="PatientInfo\EmrEditorDockPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="PatientInfo\frmEMRDoc.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PatientInfo\frmEMRDoc.Designer.cs">
      <DependentUpon>frmEMRDoc.cs</DependentUpon>
    </Compile>
    <Compile Include="PatientInfo\frmLabResultDiagram.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PatientInfo\frmLabResultDiagram.Designer.cs">
      <DependentUpon>frmLabResultDiagram.cs</DependentUpon>
    </Compile>
    <Compile Include="PatientInfo\frmSearchInPatientInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PatientInfo\FrmSearchInPatientInfo.designer.cs">
      <DependentUpon>frmSearchInPatientInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="PatientInfo\frmSearchInPatientInfoByPId.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PatientInfo\frmSearchInPatientInfoByPId.designer.cs">
      <DependentUpon>frmSearchInPatientInfoByPId.cs</DependentUpon>
    </Compile>
    <Compile Include="PatientInfo\frmSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PatientInfo\frmSelect.Designer.cs">
      <DependentUpon>frmSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="ProcessMap\frmProcessMap.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProcessMap\frmProcessMap.Designer.cs">
      <DependentUpon>frmProcessMap.cs</DependentUpon>
    </Compile>
    <Compile Include="ProcessMap\frmProcessMapDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProcessMap\frmProcessMapDesigner.Designer.cs">
      <DependentUpon>frmProcessMapDesigner.cs</DependentUpon>
    </Compile>
    <Compile Include="ProcessMap\partogramDraw.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProcessMap\partogramDraw.Designer.cs">
      <DependentUpon>partogramDraw.cs</DependentUpon>
    </Compile>
    <Compile Include="Public\Computer.cs" />
    <Compile Include="Public\DataGridViewColConfigFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Public\DataGridViewColConfigFrm.designer.cs">
      <DependentUpon>DataGridViewColConfigFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Public\DBSrv.cs" />
    <Compile Include="Public\Externs.cs" />
    <Compile Include="Public\HashItem.cs" />
    <Compile Include="Public\PrinterConfigFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Public\PrinterConfigFrm.designer.cs">
      <DependentUpon>PrinterConfigFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Public\PrinterSetup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Public\PrinterSetup.designer.cs">
      <DependentUpon>PrinterSetup.cs</DependentUpon>
    </Compile>
    <Compile Include="Public\ReportHelper.cs" />
    <Compile Include="Public\Security.cs" />
    <Compile Include="Public\TreeListViewHelper.cs" />
    <Compile Include="Public\UserManager.cs" />
    <Compile Include="Report\Externs.cs" />
    <Compile Include="Report\frmLabResultDiagram.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\frmLabResultDiagram.Designer.cs">
      <DependentUpon>frmLabResultDiagram.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\frmOrdersImplementStatus.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\frmOrdersImplementStatus.designer.cs">
      <DependentUpon>frmOrdersImplementStatus.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\frmReportBase.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\frmReportBase.designer.cs">
      <DependentUpon>frmReportBase.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\frmSearchAdmission.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\FrmSearchAdmission.designer.cs">
      <DependentUpon>frmSearchAdmission.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\FrmSearchDayBill.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\FrmSearchDayBill.designer.cs">
      <DependentUpon>FrmSearchDayBill.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\frmSearchDischarge.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\FrmSearchDischarge.designer.cs">
      <DependentUpon>frmSearchDischarge.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\FrmSearchDispens.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\FrmSearchDispens.designer.cs">
      <DependentUpon>FrmSearchDispens.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\frmSearchItemBill.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\FrmSearchItemBill.designer.cs">
      <DependentUpon>frmSearchItemBill.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\frmSearchOrders.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\frmSearchOrders.designer.cs">
      <DependentUpon>frmSearchOrders.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\FrmSearchRcptBill.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\FrmSearchRcptBill.designer.cs">
      <DependentUpon>FrmSearchRcptBill.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\OrdersImplementStatus.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\OrdersImplementStatus.designer.cs">
      <DependentUpon>OrdersImplementStatus.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\PrinterSetup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\PrinterSetup.designer.cs">
      <DependentUpon>PrinterSetup.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\ReportBaseFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\ReportBaseFrm.designer.cs">
      <DependentUpon>ReportBaseFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\ReportSampleFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Report\ReportSampleFrm.designer.cs">
      <DependentUpon>ReportSampleFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Report\valuation_detail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Report\valuation_detail.designer.cs">
      <DependentUpon>valuation_detail.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\BatchBillHelper.cs" />
    <Compile Include="Service\BedManager.cs" />
    <Compile Include="Service\CommDictManager.cs" />
    <Compile Include="Service\CustomMsgHelper.cs" />
    <Compile Include="Service\FrmSelectInhosPatientSrv.cs" />
    <Compile Include="Service\HospitalManager.cs" />
    <Compile Include="Service\InpManager.cs" />
    <Compile Include="Service\LisManager.cs" />
    <Compile Include="Service\MessagePortManager.cs" />
    <Compile Include="Service\Model\Comm\Bill_Pattern_Detail.cs" />
    <Compile Include="Service\Model\Comm\Bill_Pattern_Master.cs" />
    <Compile Include="Service\Model\Comm\DetailStateStyles.cs" />
    <Compile Include="Service\Model\Comm\Main_Menu.cs" />
    <Compile Include="Service\Model\Comm\MessageDisplay.cs" />
    <Compile Include="Service\Model\Comm\Models.cs" />
    <Compile Include="Service\Model\Comm\Model_Group.cs" />
    <Compile Include="Service\Model\Comm\NURSE_ROLE.cs" />
    <Compile Include="Service\Model\Comm\SEC_MENUS_DICT.cs" />
    <Compile Include="Service\Model\EMR\DOC_EMR_FILE_INFO.cs" />
    <Compile Include="Service\Model\INPADM\Bed_Rec.cs" />
    <Compile Include="Service\Model\NURREC\EmrDocInfo.cs" />
    <Compile Include="Service\Model\NURREC\EmrSignElementName.cs" />
    <Compile Include="Service\Model\NURREC\EmrSignResult.cs" />
    <Compile Include="Service\Model\NURREC\EmrTableSumResult.cs" />
    <Compile Include="Service\Model\NURREC\EMR_FILE_OPEN_MODE.cs" />
    <Compile Include="Service\Model\NURREC\NR_CONSULTATION_DETAIL.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_CLASS.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_EDU_BASE.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_MACRO.cs" />
    <Compile Include="Service\Model\NURREC\Nr_Dict_Plan_Base.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_PROBLEM.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_TEMP_BASE.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_TEMP_BREAK_RULE.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_TEMP_CLASS.cs" />
    <Compile Include="Service\Model\NURREC\NR_DICT_TEMP_WARDITEM.cs" />
    <Compile Include="Service\Model\NURREC\NR_DOC_CREATE.cs" />
    <Compile Include="Service\Model\NURREC\NR_DOC_Element.cs" />
    <Compile Include="Service\Model\NURREC\NR_DOC_TAG.cs" />
    <Compile Include="Service\Model\NURREC\NR_DOC_Templet.cs" />
    <Compile Include="Service\Model\NURREC\NR_DOC_Templet_Filter.cs" />
    <Compile Include="Service\Model\NURREC\NR_DOC_Templet_Status.cs" />
    <Compile Include="Service\Model\NURREC\NR_EDU_RECORD.cs" />
    <Compile Include="Service\Model\NURREC\NR_POCT_RECORD.cs" />
    <Compile Include="Service\Model\NURREC\NR_SIGNATURE.cs" />
    <Compile Include="Service\Model\NURREC\NR_SUBSET_VS_CLASS.cs" />
    <Compile Include="Service\Model\NURREC\NR_TEMP_SIGNS_REC.cs" />
    <Compile Include="Service\Model\NURREC\NR_TRANS_SCHE_DETAIL.cs" />
    <Compile Include="Service\Model\NURREC\PatientEmrDocInfo.cs" />
    <Compile Include="Service\NurseManager.cs" />
    <Compile Include="Service\RecordLockManager.cs" />
    <Compile Include="Service\StaffManager.cs" />
    <Compile Include="Service\View\frmDoctorGroupSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Service\View\frmDoctorGroupSelect.Designer.cs">
      <DependentUpon>frmDoctorGroupSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\View\frmInpPatSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Service\View\frmInpPatSelect.Designer.cs">
      <DependentUpon>frmInpPatSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\View\frmLisResultAlarm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Service\View\frmLisResultAlarm.Designer.cs">
      <DependentUpon>frmLisResultAlarm.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\View\frmMsgQueueList.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Service\View\frmMsgQueueList.Designer.cs">
      <DependentUpon>frmMsgQueueList.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\View\frmSelectInhosPatient.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Service\View\FrmSelectInhosPatient.Designer.cs">
      <DependentUpon>frmSelectInhosPatient.cs</DependentUpon>
    </Compile>
    <Compile Include="Service\View\PatientSelectorFrm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Service\View\PatientSelectorFrm.Designer.cs">
      <DependentUpon>PatientSelectorFrm.cs</DependentUpon>
    </Compile>
    <Compile Include="Srv\ADTSrv\ADTManager.cs" />
    <Compile Include="Srv\ADTSrv\DoctorManager.cs" />
    <Compile Include="Srv\ADTSrv\PatientDbI.cs" />
    <Compile Include="Srv\ADTSrv\SrvBedExchange.cs" />
    <Compile Include="Srv\ADTSrv\SrvBedSideCard.cs" />
    <Compile Include="Srv\ADTSrv\SrvCancelDischarge.cs" />
    <Compile Include="Srv\ADTSrv\SrvCancelTransfer.cs" />
    <Compile Include="Srv\ADTSrv\SrvCencelPatientIn.cs" />
    <Compile Include="Srv\ADTSrv\SrvContractBed.cs" />
    <Compile Include="Srv\ADTSrv\SrvDailyLog.cs" />
    <Compile Include="Srv\ADTSrv\SrvDisChargeProc.cs" />
    <Compile Include="Srv\ADTSrv\SrvNewBornAdmit.cs" />
    <Compile Include="Srv\ADTSrv\SrvNewBornEval.cs" />
    <Compile Include="Srv\ADTSrv\SrvNewBornInput.cs" />
    <Compile Include="Srv\ADTSrv\SrvPatientIn.cs" />
    <Compile Include="Srv\ADTSrv\SrvPatientInfoModify.cs" />
    <Compile Include="Srv\ADTSrv\SrvPatOutHospital.cs" />
    <Compile Include="Srv\ADTSrv\SrvPreOut.cs" />
    <Compile Include="Srv\ADTSrv\SrvTransferProc.cs" />
    <Compile Include="Srv\ADTSrv\SrvWardOverView.cs" />
    <Compile Include="Srv\BillSrv\billItemRefundHdpartSrv.cs" />
    <Compile Include="Srv\BillSrv\billPatternCallSrv.cs" />
    <Compile Include="Srv\BillSrv\billPublicSrv.cs" />
    <Compile Include="Srv\BillSrv\DischargedRefundHdpartSrv.cs" />
    <Compile Include="Srv\BillSrv\drawFinancialSrv.cs" />
    <Compile Include="Srv\BillSrv\getPrepaymentsSrv.cs" />
    <Compile Include="Srv\BillSrv\inputSettingSrv.cs" />
    <Compile Include="Srv\BillSrv\muchPatientItemSrv.cs" />
    <Compile Include="Srv\BillSrv\noRecipeNoCheckSrv.cs" />
    <Compile Include="Srv\BillSrv\orderCostVerifySrv.cs" />
    <Compile Include="Srv\BillSrv\pachOrderBillSrv.cs" />
    <Compile Include="Srv\BillSrv\patBillMiscItemPatchSrv.cs" />
    <Compile Include="Srv\BillSrv\PatientBillSrv.cs" />
    <Compile Include="Srv\BillSrv\validateUserSrv.cs" />
    <Compile Include="Srv\BillSrv\valuationProcessingSrv.cs" />
    <Compile Include="Srv\MaterialSrv\srvDispenseReq.cs" />
    <Compile Include="Srv\MaterialSrv\srvExApplication.cs" />
    <Compile Include="Srv\MaterialSrv\srvHighQuality.cs" />
    <Compile Include="Srv\MaterialSrv\srvHighQualityRetun.cs" />
    <Compile Include="Srv\MaterialSrv\srvPatsInHospital.cs" />
    <Compile Include="Srv\MaterialSrv\srvPpcOrderPre.cs" />
    <Compile Include="Srv\MaterialSrv\srvPpcOrderPre_BaseData.cs" />
    <Compile Include="Srv\MaterialSrv\srvPrescHandBack.cs" />
    <Compile Include="Srv\MaterialSrv\srvprescHandBackQuery.cs" />
    <Compile Include="Srv\MaterialSrv\srvPrescQueryList.cs" />
    <Compile Include="Srv\NurRecSettingSrv\EMRHelper.cs" />
    <Compile Include="Srv\NurRecSettingSrv\FormMacroTargetSrv.cs" />
    <Compile Include="Srv\NurRecSettingSrv\FormPDAConfigSrv.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvKnowledgeDept.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvKnowledgeStand.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictClass.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictEduBase.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictEduItem.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictInspection.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictMacro.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictPlanBase.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictPlanTemplet.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrDictTransReason.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrRecordItem.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNRSubsetPDACATALOG.cs" />
    <Compile Include="Srv\NurRecSettingSrv\srvNrSubsetVsClass.cs" />
    <Compile Include="Srv\NurRecSrv\DocEMRSrv.cs" />
    <Compile Include="Srv\NurRecSrv\DrugsPoisonHempManager.cs" />
    <Compile Include="Srv\NurRecSrv\EmrDocHelper.cs" />
    <Compile Include="Srv\NurRecSrv\EmrDocSrv.cs" />
    <Compile Include="Srv\NurRecSrv\frmNursingEmrDocSrv.cs" />
    <Compile Include="Srv\NurRecSrv\GoodsMaintainManager.cs" />
    <Compile Include="Srv\NurRecSrv\HealthEduManager.cs" />
    <Compile Include="Srv\NurRecSrv\ImportOrdersServ.cs" />
    <Compile Include="Srv\NurRecSrv\InpSrv.cs" />
    <Compile Include="Srv\NurRecSrv\ItemsRecordedManager.cs" />
    <Compile Include="Srv\NurRecSrv\NRDocManager.cs" />
    <Compile Include="Srv\NurRecSrv\NRDocRefDataManager.cs" />
    <Compile Include="Srv\NurRecSrv\NRIndexManager.cs" />
    <Compile Include="Srv\NurRecSrv\NurseShiftWorkManager.cs" />
    <Compile Include="Srv\NurRecSrv\NursingConsultSrv.cs" />
    <Compile Include="Srv\NurRecSrv\NursingRoundManager.cs" />
    <Compile Include="Srv\NurRecSrv\OrdersBrowserSrv.cs" />
    <Compile Include="Srv\NurRecSrv\PDAConfigSrv.cs" />
    <Compile Include="Srv\NurRecSrv\PoctObseverManager.cs" />
    <Compile Include="Srv\NurRecSrv\PoctRecordManager.cs" />
    <Compile Include="Srv\NurRecSrv\RecordLockSrv.cs" />
    <Compile Include="Srv\NurRecSrv\srvEduItemTypeContentSet.cs" />
    <Compile Include="Srv\NurRecSrv\srvfrmEmrEditorControlDev.cs" />
    <Compile Include="Srv\NurRecSrv\srvInOutTemp.cs" />
    <Compile Include="Srv\NurRecSrv\srvNRCount.cs" />
    <Compile Include="Srv\NurRecSrv\SrvNREvaluatePain.cs" />
    <Compile Include="Srv\NurRecSrv\srvNrTempletManager.cs" />
    <Compile Include="Srv\NurRecSrv\srvNursingPlan.cs" />
    <Compile Include="Srv\NurRecSrv\SrvSearchRecord.cs" />
    <Compile Include="Srv\NurRecSrv\srvSelectPatientHeat.cs" />
    <Compile Include="Srv\NurRecSrv\srvSuccessionIsSet.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\HISDictDbI.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\HospitalDbI.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\PatientDbI.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\ScoreManger.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\srvDataQuery.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\srvInOutCount.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\srvInOutTemp.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\srvLineChart.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\srvRecordDesign.cs" />
    <Compile Include="Srv\NursingRecordSheetSrv\srvSevereRecord.cs" />
    <Compile Include="Srv\OrdersSrv\CollectionHelper.cs" />
    <Compile Include="Srv\OrdersSrv\DictManager.cs" />
    <Compile Include="Srv\OrdersSrv\OrderExecuteDbi.cs" />
    <Compile Include="Srv\OrdersSrv\OrderManager.cs" />
    <Compile Include="Srv\OrdersSrv\OrderSplitPrintDbi.cs" />
    <Compile Include="Srv\OrdersSrv\OrdersSplitter.cs" />
    <Compile Include="Srv\OrdersSrv\srvBrowseOrderError.cs" />
    <Compile Include="Srv\OrdersSrv\srvBrowseOrders.cs" />
    <Compile Include="Srv\OrdersSrv\srvOrderAudit.cs" />
    <Compile Include="Srv\OrdersSrv\srvOrderPrintOption.cs" />
    <Compile Include="Srv\OrdersSrv\srvOrdersBook.cs" />
    <Compile Include="Srv\OrdersSrv\srvOrdersSplitPrint.cs" />
    <Compile Include="Srv\OrdersSrv\srvOrderStopDate.cs" />
    <Compile Include="Srv\OrdersSrv\srvPrintOrderBook.cs" />
    <Compile Include="Srv\OrdersSrv\srvStaffSelect.cs" />
    <Compile Include="Srv\OrdersSrv\TestsPecimenGather.cs" />
    <Compile Include="Srv\PatientInfoSrv\srvSearchInPatientInfo.cs" />
    <Compile Include="Srv\ReportSrv\srvBloodSugar.cs" />
    <Compile Include="Srv\ReportSrv\srvSearchBloodWasDelivery.cs" />
    <Compile Include="Srv\ReportSrv\srvSearchItemBill.cs" />
    <Compile Include="Srv\ReportSrv\srvSearchOverdraft.cs" />
    <Compile Include="Srv\RuleSrv\srvRuleConsult.cs" />
    <Compile Include="Srv\RuleSrv\srvRuleDownRec.cs" />
    <Compile Include="Srv\RuleSrv\srvRuleManage.cs" />
    <Compile Include="Srv\RuleSrv\srvRulePersonInfo.cs" />
    <Compile Include="Srv\RuleSrv\srvViewer.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvBedRec.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvDictEvalItem.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvInpManager.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvMonitorClassConfig.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvNrConfigerParameter.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvNrDictAdministrationGroup.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvNrDictArticle.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvNrDictNurseRole.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvNursingAccidentConfig.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvPrescDoctDelectStorage.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvScreenConfig.cs" />
    <Compile Include="Srv\SystemSettingSrv\srvSelectOrdersDisp.cs" />
    <Compile Include="SystemSetting\frmBedGroupDetail.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmBedGroupDetail.Designer.cs">
      <DependentUpon>frmBedGroupDetail.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmBedGroupManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmBedGroupManager.Designer.cs">
      <DependentUpon>frmBedGroupManager.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmBedNurseFollowManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmBedNurseFollowManager.Designer.cs">
      <DependentUpon>frmBedNurseFollowManager.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmBedRec.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmBedRec.Designer.cs">
      <DependentUpon>frmBedRec.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmDictEvalItem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\FrmDictEvalItem.Designer.cs">
      <DependentUpon>frmDictEvalItem.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmMonitorClassConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmMonitorClassConfig.Designer.cs">
      <DependentUpon>frmMonitorClassConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmNrConfigerParameter.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmNrConfigerParameter.Designer.cs">
      <DependentUpon>frmNrConfigerParameter.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmNrDictArticle.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmNrDictArticle.Designer.cs">
      <DependentUpon>frmNrDictArticle.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmNrDictNurseRole.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmNrDictNurseRole.Designer.cs">
      <DependentUpon>frmNrDictNurseRole.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmNursingAccidentConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmNursingAccidentConfig.Designer.cs">
      <DependentUpon>frmNursingAccidentConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\FrmNusingDict.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\FrmNusingDict.Designer.cs">
      <DependentUpon>FrmNusingDict.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmOrdersExecGroupConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmOrdersExecGroupConfig.Designer.cs">
      <DependentUpon>frmOrdersExecGroupConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmOrdersExecGroupInputD.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmOrdersExecGroupInputD.Designer.cs">
      <DependentUpon>frmOrdersExecGroupInputD.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmOrdersExecGroupInputM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmOrdersExecGroupInputM.Designer.cs">
      <DependentUpon>frmOrdersExecGroupInputM.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmPrescDoctDelectStorage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmPrescDoctDelectStorage.Designer.cs">
      <DependentUpon>frmPrescDoctDelectStorage.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmScnMonitor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmScnMonitor.Designer.cs">
      <DependentUpon>frmScnMonitor.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmScnPersonnel.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmScnPersonnel.Designer.cs">
      <DependentUpon>frmScnPersonnel.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmScreenConfig.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmScreenConfig.Designer.cs">
      <DependentUpon>frmScreenConfig.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmSelectOrdersDisp.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmSelectOrdersDisp.designer.cs">
      <DependentUpon>frmSelectOrdersDisp.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmStaffPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmStaffPrint.Designer.cs">
      <DependentUpon>frmStaffPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="SystemSetting\frmToDoItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SystemSetting\frmToDoItems.designer.cs">
      <DependentUpon>frmToDoItems.cs</DependentUpon>
    </Compile>
    <Compile Include="Thermogram\frmPatSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Thermogram\frmPatSelect.Designer.cs">
      <DependentUpon>frmPatSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="Thermogram\frmPrintingSelection.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Thermogram\frmPrintingSelection.Designer.cs">
      <DependentUpon>frmPrintingSelection.cs</DependentUpon>
    </Compile>
    <Compile Include="Thermogram\frmThermogram.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Thermogram\frmThermogram.Designer.cs">
      <DependentUpon>frmThermogram.cs</DependentUpon>
    </Compile>
    <Compile Include="Thermogram\frmThermogramDesigner.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Thermogram\frmThermogramDesigner.Designer.cs">
      <DependentUpon>frmThermogramDesigner.cs</DependentUpon>
    </Compile>
    <Compile Include="Thermogram\Program.cs" />
    <Compile Include="Thermogram\tempDraw.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Thermogram\tempDraw.Designer.cs">
      <DependentUpon>tempDraw.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="ADT\frmAdtLog.resx">
      <DependentUpon>frmAdtLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmBedExchange.resx">
      <DependentUpon>frmBedExchange.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmBedSideCard.resx">
      <DependentUpon>frmBedSideCard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmBedsideCardWristbandPrint.resx">
      <DependentUpon>frmBedsideCardWristbandPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmBedsideMultiPrint.resx">
      <DependentUpon>frmBedsideMultiPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmCancelDischarge.resx">
      <DependentUpon>frmCancelDischarge.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmCancelPatientIn.resx">
      <DependentUpon>frmCancelPatientIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmCancelTransfer.resx">
      <DependentUpon>frmCancelTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmContractBed.resx">
      <DependentUpon>frmContractBed.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmContractRoom.resx">
      <DependentUpon>frmContractRoom.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmDailyLog.resx">
      <DependentUpon>frmDailyLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmDisChargeProc.resx">
      <DependentUpon>frmDisChargeProc.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmNewbornDetail.resx">
      <DependentUpon>frmNewbornDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmNewBornEval.resx">
      <DependentUpon>FrmNewBornEval.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmNewbornIn.resx">
      <DependentUpon>frmNewbornIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmNewBornInput.resx">
      <DependentUpon>FrmNewBornInput.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmNewBornList.resx">
      <DependentUpon>FrmNewBornList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmDisChargeCheck.resx">
      <DependentUpon>frmDisChargeCheck.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmPatientIn.resx">
      <DependentUpon>frmPatientIn.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmPatientInfoModify.resx">
      <DependentUpon>FrmPatientInfoModify.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmPatOutHospital.resx">
      <DependentUpon>FrmPatOutHospital.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmPreOut.resx">
      <DependentUpon>frmPreOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmSelectCencelFlag.resx">
      <DependentUpon>FrmSelectCencelFlag.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\frmSetThreeLevelDoctors.resx">
      <DependentUpon>frmSetThreeLevelDoctors.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\FrmTransferProc.resx">
      <DependentUpon>frmTransferProc.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\MainFrm2.resx">
      <DependentUpon>MainFrm2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\OrderMsg.resx">
      <DependentUpon>OrderMsg.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\rptBirthCertificate.resx">
      <DependentUpon>rptBirthCertificate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\TextInputFrm.resx">
      <DependentUpon>TextInputFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\WardOverViewFrm.resx">
      <DependentUpon>WardOverViewFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ADT\WristBandTypeSelectFrm.resx">
      <DependentUpon>WristBandTypeSelectFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmBillPatternCall.resx">
      <DependentUpon>frmBillPatternCall.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmDischargedRefundHdpart.resx">
      <DependentUpon>frmDischargedRefundHdpart.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmDrawFinancial.resx">
      <DependentUpon>frmDrawFinancial.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmGetPrepayments.resx">
      <DependentUpon>frmGetPrepayments.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmHighQuality.resx">
      <DependentUpon>frmHighQuality.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmHighQualityRetun.resx">
      <DependentUpon>frmHighQualityRetun.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmInputSetting.resx">
      <DependentUpon>frmInputsetting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmMuchPatientItem.resx">
      <DependentUpon>frmMuchPatientItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmNoRecipeNoCheck.resx">
      <DependentUpon>frmNoRecipeNoCheck.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmOrderCostVerify.resx">
      <DependentUpon>frmOrderCostVerify.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmOrderCostVerifyOut.resx">
      <DependentUpon>frmOrderCostVerifyOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmOrderCostVerifyOutPat.resx">
      <DependentUpon>frmOrderCostVerifyOutPat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmPatBillMiscItemPatch.resx">
      <DependentUpon>frmPatBillMiscItemPatch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmSetDate.resx">
      <DependentUpon>frmSetDate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\frmValidateUser.resx">
      <DependentUpon>frmValidateUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Bill\valuation_detail.resx">
      <DependentUpon>valuation_detail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmDispenseReq.resx">
      <DependentUpon>frmDispenseReq.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmExApplication.resx">
      <DependentUpon>frmExApplication.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmHighQuality.resx">
      <DependentUpon>frmHighQuality.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmHighQualityRetun.resx">
      <DependentUpon>frmHighQualityRetun.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\FrmInpSettleCancelCheck.resx">
      <DependentUpon>FrmInpSettleCancelCheck.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\FrmMedcostQuery.resx">
      <DependentUpon>FrmMedcostQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\FrmMedcostQueryPatientList.resx">
      <DependentUpon>FrmMedcostQueryPatientList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmPpcOrderPre.resx">
      <DependentUpon>frmPpcOrderPre.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmPrescHandBack.resx">
      <DependentUpon>frmPrescHandBack.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmPrescHandBackByDisp.resx">
      <DependentUpon>frmPrescHandBackByDisp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\FrmPrescHandbackHistory.resx">
      <DependentUpon>FrmPrescHandbackHistory.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmprescHandBackQuery.resx">
      <DependentUpon>frmprescHandBackQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmPrescQueryList.resx">
      <DependentUpon>frmPrescQueryList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frmwApplicationNew.resx">
      <DependentUpon>frmwApplicationNew.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Material\frPatsInHospital.resx">
      <DependentUpon>frPatsInHospital.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NuradmRule\frmPDFViewer.resx">
      <DependentUpon>frmPDFViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NuradmRule\frmRulePersonInfo.resx">
      <DependentUpon>frmRulePersonInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NuradmRule\frmWordViewer.resx">
      <DependentUpon>frmWordViewer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\FormAddParam.resx">
      <DependentUpon>FormAddParam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\FormMacroTarget.resx">
      <DependentUpon>FormMacroTarget.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\FormPDACataLog.resx">
      <DependentUpon>FormPDACataLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmKnowledgeDept.resx">
      <DependentUpon>frmKnowledgeDept.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmKnowledgeStand.resx">
      <DependentUpon>frmKnowledgeStand.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictClass.resx">
      <DependentUpon>frmNrDictClass.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictEduBase.resx">
      <DependentUpon>frmNrDictEduBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictEduItem.resx">
      <DependentUpon>frmNrDictEduItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictInspection.resx">
      <DependentUpon>frmNrDictInspection.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictMacro.resx">
      <DependentUpon>frmNrDictMacro.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictPlanBase.resx">
      <DependentUpon>frmNrDictPlanBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictPlanTemplet.resx">
      <DependentUpon>frmNrDictPlanTemplet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNrDictTransReason.resx">
      <DependentUpon>frmNrDictTransReason.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNRRecordItem.resx">
      <DependentUpon>frmNRRecordItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNRSubsetPDACATALOG.resx">
      <DependentUpon>frmNRSubsetPDACATALOG.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRecSetting\frmNRSubsetVSClass.resx">
      <DependentUpon>frmNRSubsetVSClass.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\DlgLineSpacingSetting.resx">
      <DependentUpon>DlgLineSpacingSetting.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\DocEmrUC.resx">
      <DependentUpon>DocEmrUC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\DocRenameFrm.resx">
      <DependentUpon>DocRenameFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\EMRDocToolBaseFrm.resx">
      <DependentUpon>EMRDocToolBaseFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FileExitWarningDialog.resx">
      <DependentUpon>FileExitWarningDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FormNRDocTempletFileAdd.resx">
      <DependentUpon>FormNRDocTempletFileAdd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FormNRDocTempletSelect.resx">
      <DependentUpon>FormNRDocTempletSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FormPDACataLog.resx">
      <DependentUpon>FormPDACataLog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FormPDAConfig.resx">
      <DependentUpon>FormPDAConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FormSqlError.resx">
      <DependentUpon>FormSqlError.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmAddHealthEduDiag.resx">
      <DependentUpon>frmAddHealthEduDiag.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmAddPlanRec.resx">
      <DependentUpon>frmAddPlanRec.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmAddPoctObserve.resx">
      <DependentUpon>frmAddPoctObserve.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmAddPoctRecord.resx">
      <DependentUpon>frmAddPoctRecord.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmAddRoundRec.resx">
      <DependentUpon>frmAddRoundRec.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmAddStructedElement.resx">
      <DependentUpon>frmAddStructedElement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmBrowsemr.resx">
      <DependentUpon>frmBrowsemr.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmBrowseOrders.resx">
      <DependentUpon>frmBrowseOrders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmBrowsExam.resx">
      <DependentUpon>frmBrowsExam.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmBrowsLab.resx">
      <DependentUpon>frmBrowsLab.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmBrowstem.resx">
      <DependentUpon>frmBrowsTem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmBrowsTemp.resx">
      <DependentUpon>frmBrowsTemp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmCatheter.resx">
      <DependentUpon>frmCatheter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmConsultSearch.resx">
      <DependentUpon>frmConsultSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmDocEMRShow.resx">
      <DependentUpon>frmDocEMRShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmDocRename.resx">
      <DependentUpon>frmDocRename.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmDrugsPoisonHemp.resx">
      <DependentUpon>frmDrugsPoisonHemp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmEduItemTypeContentSet.resx">
      <DependentUpon>frmEduItemTypeContentSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmFIleshow.resx">
      <DependentUpon>frmFIleshow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmGoodsMaintain.resx">
      <DependentUpon>frmGoodsMaintain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmHealthEdu.resx">
      <DependentUpon>frmHealthEdu.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmHealthEduItemSelector.resx">
      <DependentUpon>frmHealthEduItemSelector.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmHierarchyTree.resx">
      <DependentUpon>frmHierarchyTree.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmItemsRecorded.resx">
      <DependentUpon>frmItemsRecorded.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNRCount.resx">
      <DependentUpon>frmNRCount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNRDocTempletChangeTitle.resx">
      <DependentUpon>frmNRDocTempletChangeTitle.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNRDocTempletFile.resx">
      <DependentUpon>frmNRDocTempletFile.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNRDocTempletSearch.resx">
      <DependentUpon>frmNRDocTempletSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNREvaluatePain.resx">
      <DependentUpon>frmNREvaluatePain.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNREvaluatePain_DarwFigure.resx">
      <DependentUpon>frmNREvaluatePain_DarwFigure.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNrGutter.resx">
      <DependentUpon>frmNrGutter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNrRecordSearch.resx">
      <DependentUpon>frmNrRecordSearch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNurRecUnLock.resx">
      <DependentUpon>frmNurRecUnLock.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNursingConsult.resx">
      <DependentUpon>frmNursingConsult.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNursingDoc.resx">
      <DependentUpon>frmNursingDoc.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNursingPlan.resx">
      <DependentUpon>frmNursingPlan.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmNursingRound.resx">
      <DependentUpon>frmNursingRound.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmOldDocEMRShow.resx">
      <DependentUpon>frmOldDocEMRShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmOperation.resx">
      <DependentUpon>frmOperation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FrmPatientEMRCenter.resx">
      <DependentUpon>FrmPatientEMRCenter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FrmPatList.resx">
      <DependentUpon>FrmPatList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmPatNurseFollowManager.resx">
      <DependentUpon>frmPatNurseFollowManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmPatSelector.resx">
      <DependentUpon>frmPatSelector.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmPlanEva.resx">
      <DependentUpon>frmPlanEva.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmPoctRecord.resx">
      <DependentUpon>frmPoctRecord.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmSectionName.resx">
      <DependentUpon>frmSectionName.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmSelectNRDocTemplete.resx">
      <DependentUpon>frmSelectNRDocTemplete.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmSelectPatientHeat.resx">
      <DependentUpon>frmSelectPatientHeat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmShiftWork.resx">
      <DependentUpon>frmShiftWork.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmSignsTypeMessage.resx">
      <DependentUpon>frmSignsTypeMessage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmSuccessionIsSet.resx">
      <DependentUpon>frmSuccessionIsSet.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\FrmTempSign.resx">
      <DependentUpon>FrmTempSign.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\frmToDoList.resx">
      <DependentUpon>frmToDoList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\ImportOrdersDialog.resx">
      <DependentUpon>ImportOrdersDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\InOutSumColfrm.resx">
      <DependentUpon>InOutSumColfrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\InOutSumDialog.resx">
      <DependentUpon>InOutSumDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\InOutTempfrm.resx">
      <DependentUpon>InOutTempfrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\InpPatSelectFrm.resx">
      <DependentUpon>InpPatSelectFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\NurEmrTempletFrm.resx">
      <DependentUpon>NurEmrTempletFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\NurEmrUC.resx">
      <DependentUpon>NurEmrUC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\NurEmrVersionListFrm.resx">
      <DependentUpon>NurEmrVersionListFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\RegularTextFrm.resx">
      <DependentUpon>RegularTextFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\SelMarcroListFrm.resx">
      <DependentUpon>SelMarcroListFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\ToothExpInput.resx">
      <DependentUpon>ToothExpInput.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\ucNRCount.resx">
      <DependentUpon>ucNRCount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NurRec\UpdMarcroListFrm.resx">
      <DependentUpon>UpdMarcroListFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\DictManagerFrm.resx">
      <DependentUpon>DictManagerFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\DictSelectorFrm.resx">
      <DependentUpon>DictSelectorFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\DocEmrUC.resx">
      <DependentUpon>DocEmrUC.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmDataQuery.resx">
      <DependentUpon>frmDataQuery.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmDocEMRShow.resx">
      <DependentUpon>frmDocEMRShow.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmFood.resx">
      <DependentUpon>frmFood.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmInOutCount.resx">
      <DependentUpon>frmInOutCount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmLineChart.resx">
      <DependentUpon>frmLineChart.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmNursingRecord.resx">
      <DependentUpon>frmNursingRecord.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\FrmOrders.resx">
      <DependentUpon>FrmOrders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmPrintView.resx">
      <DependentUpon>frmPrintView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmRecordBatInp.resx">
      <DependentUpon>frmRecordBatInp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmRecordDesigner.resx">
      <DependentUpon>frmRecordDesigner.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmRecordView.resx">
      <DependentUpon>frmRecordView.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmSevereRecord.resx">
      <DependentUpon>frmSevereRecord.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\frmSum.resx">
      <DependentUpon>frmSum.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\FrmVital.resx">
      <DependentUpon>FrmVital.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\InOutTempForm.resx">
      <DependentUpon>InOutTempForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\OptionSelectionFrm.resx">
      <DependentUpon>OptionSelectionFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\PatientList.resx">
      <DependentUpon>PatientList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\PatientListWardFrm.resx">
      <DependentUpon>PatientListWardFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\PatientOut.resx">
      <DependentUpon>PatientOut.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\PatientSearchFrm.resx">
      <DependentUpon>PatientSearchFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\PrintModeSelectFrm.resx">
      <DependentUpon>PrintModeSelectFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\RadioValueSelectFrm.resx">
      <DependentUpon>RadioValueSelectFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\RassScore.resx">
      <DependentUpon>RassScore.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\ScoreDesignForm.resx">
      <DependentUpon>ScoreDesignForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\ScoreEvaluate.resx">
      <DependentUpon>ScoreEvaluate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NursingRecordSheet\TemplateViewFrm.resx">
      <DependentUpon>TemplateViewFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmBrowseOrders.resx">
      <DependentUpon>frmBrowseOrders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmBrowseOrdersError.resx">
      <DependentUpon>frmBrowseOrdersError.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmCancelOrderAudit.resx">
      <DependentUpon>frmCancelOrderAudit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmDictFeeItem.resx">
      <DependentUpon>frmDictFeeItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrderAudit.resx">
      <DependentUpon>frmOrderAudit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrderAuditWard.resx">
      <DependentUpon>frmOrderAuditWard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrderCosts.resx">
      <DependentUpon>frmOrderCosts.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrderPrint.resx">
      <DependentUpon>frmOrderPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrderPrintOption.resx">
      <DependentUpon>frmOrderPrintOption.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrdersBook.resx">
      <DependentUpon>frmOrdersBook.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrdersExecute.resx">
      <DependentUpon>frmOrdersExecute.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrdersExecuteMemo.resx">
      <DependentUpon>frmOrdersExecuteMemo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrdersSplitPrint.resx">
      <DependentUpon>frmOrdersSplitPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrdersTest.resx">
      <DependentUpon>frmOrdersTest.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrderStopDate.resx">
      <DependentUpon>frmOrderStopDate.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmOrderVerifyList.resx">
      <DependentUpon>frmOrderVerifyList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmPrintOrderBook.resx">
      <DependentUpon>frmPrintOrderBook.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmSearchOrder.resx">
      <DependentUpon>frmSearchOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmStaffSelect.resx">
      <DependentUpon>frmStaffSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmTestItemDetail.resx">
      <DependentUpon>frmTestItemDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\frmTestsPecimenGather.resx">
      <DependentUpon>frmTestsPecimenGather.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\OrderSplitTestFrm.resx">
      <DependentUpon>OrderSplitTestFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\TestSpeciGatherFrm.resx">
      <DependentUpon>TestSpeciGatherFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Orders\TestsPecimenGatherFrm.resx">
      <DependentUpon>TestsPecimenGatherFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PatientInfo\EmrEditorControlDev.resx">
      <DependentUpon>EmrEditorControlDev.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PatientInfo\EmrEditorDockPanel.resx">
      <DependentUpon>EmrEditorDockPanel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PatientInfo\frmEMRDoc.resx">
      <DependentUpon>frmEMRDoc.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PatientInfo\frmLabResultDiagram.resx">
      <DependentUpon>frmLabResultDiagram.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PatientInfo\FrmSearchInPatientInfo.resx">
      <DependentUpon>frmSearchInPatientInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PatientInfo\frmSearchInPatientInfoByPId.resx">
      <DependentUpon>frmSearchInPatientInfoByPId.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PatientInfo\frmSelect.resx">
      <DependentUpon>frmSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProcessMap\frmProcessMap.resx">
      <DependentUpon>frmProcessMap.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProcessMap\frmProcessMapDesigner.resx">
      <DependentUpon>frmProcessMapDesigner.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProcessMap\partogramDraw.resx">
      <DependentUpon>partogramDraw.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Public\DataGridViewColConfigFrm.resx">
      <DependentUpon>DataGridViewColConfigFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Public\PrinterConfigFrm.resx">
      <DependentUpon>PrinterConfigFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Public\PrinterSetup.resx">
      <DependentUpon>PrinterSetup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\frmLabResultDiagram.resx">
      <DependentUpon>frmLabResultDiagram.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\frmOrdersImplementStatus.resx">
      <DependentUpon>frmOrdersImplementStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\frmReportBase.resx">
      <DependentUpon>frmReportBase.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\FrmSearchAdmission.resx">
      <DependentUpon>frmSearchAdmission.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\FrmSearchDayBill.resx">
      <DependentUpon>FrmSearchDayBill.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\FrmSearchDischarge.resx">
      <DependentUpon>frmSearchDischarge.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\FrmSearchDispens.resx">
      <DependentUpon>FrmSearchDispens.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\FrmSearchItemBill.resx">
      <DependentUpon>frmSearchItemBill.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\frmSearchOrders.resx">
      <DependentUpon>frmSearchOrders.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\FrmSearchRcptBill.resx">
      <DependentUpon>FrmSearchRcptBill.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\OrdersImplementStatus.resx">
      <DependentUpon>OrdersImplementStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\PrinterSetup.resx">
      <DependentUpon>PrinterSetup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\ReportBaseFrm.resx">
      <DependentUpon>ReportBaseFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\ReportSampleFrm.resx">
      <DependentUpon>ReportSampleFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Report\valuation_detail.resx">
      <DependentUpon>valuation_detail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Service\View\frmDoctorGroupSelect.resx">
      <DependentUpon>frmDoctorGroupSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Service\View\frmInpPatSelect.resx">
      <DependentUpon>frmInpPatSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Service\View\frmLisResultAlarm.resx">
      <DependentUpon>frmLisResultAlarm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Service\View\frmMsgQueueList.resx">
      <DependentUpon>frmMsgQueueList.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Service\View\FrmSelectInhosPatient.resx">
      <DependentUpon>frmSelectInhosPatient.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Service\View\PatientSelectorFrm.resx">
      <DependentUpon>PatientSelectorFrm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmBedGroupDetail.resx">
      <DependentUpon>frmBedGroupDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmBedGroupManager.resx">
      <DependentUpon>frmBedGroupManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmBedNurseFollowManager.resx">
      <DependentUpon>frmBedNurseFollowManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmBedRec.resx">
      <DependentUpon>frmBedRec.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\FrmDictEvalItem.resx">
      <DependentUpon>frmDictEvalItem.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmMonitorClassConfig.resx">
      <DependentUpon>frmMonitorClassConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmNrConfigerParameter.resx">
      <DependentUpon>frmNrConfigerParameter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmNrDictArticle.resx">
      <DependentUpon>frmNrDictArticle.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmNrDictNurseRole.resx">
      <DependentUpon>frmNrDictNurseRole.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmNursingAccidentConfig.resx">
      <DependentUpon>frmNursingAccidentConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\FrmNusingDict.resx">
      <DependentUpon>FrmNusingDict.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmOrdersExecGroupConfig.resx">
      <DependentUpon>frmOrdersExecGroupConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmOrdersExecGroupInputD.resx">
      <DependentUpon>frmOrdersExecGroupInputD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmOrdersExecGroupInputM.resx">
      <DependentUpon>frmOrdersExecGroupInputM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmPrescDoctDelectStorage.resx">
      <DependentUpon>frmPrescDoctDelectStorage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmScnMonitor.resx">
      <DependentUpon>frmScnMonitor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmScnPersonnel.resx">
      <DependentUpon>frmScnPersonnel.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmScreenConfig.resx">
      <DependentUpon>frmScreenConfig.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmSelectOrdersDisp.resx">
      <DependentUpon>frmSelectOrdersDisp.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmStaffPrint.resx">
      <DependentUpon>frmStaffPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SystemSetting\frmToDoItems.resx">
      <DependentUpon>frmToDoItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Thermogram\frmPatSelect.resx">
      <DependentUpon>frmPatSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Thermogram\frmPrintingSelection.resx">
      <DependentUpon>frmPrintingSelection.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Thermogram\frmThermogram.resx">
      <DependentUpon>frmThermogram.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Thermogram\frmThermogramDesigner.resx">
      <DependentUpon>frmThermogramDesigner.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Thermogram\tempDraw.resx">
      <DependentUpon>tempDraw.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup />
  <ItemGroup Condition="$(VisualStudioVersion) == '15.0'">
    <Reference Include="Microsoft.Data.Tools.Schema.Sql, Version=13.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>$(SSDTPath)\Microsoft.Data.Tools.Schema.Sql.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Tools.Schema.Sql.UnitTesting, Version=15.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>$(SSDTUnitTestPath)\Microsoft.Data.Tools.Schema.Sql.UnitTesting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Data.Tools.Schema.Sql.UnitTestingAdapter, Version=15.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>$(SSDTUnitTestPath)\Microsoft.Data.Tools.Schema.Sql.UnitTestingAdapter.dll</HintPath>
      <Private>True</Private>
    </Reference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <SsdtUnitTestVersion>3.1</SsdtUnitTestVersion>
  </PropertyGroup>
  <Import Project="$(SQLDBExtensionsRefPath)\Microsoft.Data.Tools.Schema.Sql.UnitTesting.targets" Condition="$(VisualStudioVersion) != '15.0' And '$(SQLDBExtensionsRefPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\SSDT\Microsoft.Data.Tools.Schema.Sql.UnitTesting.targets" Condition="$(VisualStudioVersion) != '15.0' And '$(SQLDBExtensionsRefPath)' == ''" />
</Project>