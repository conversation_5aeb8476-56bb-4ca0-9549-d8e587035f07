===============================================================================
                门诊医生站费用计算错误 - 安全修复实施方案
===============================================================================
项目：Tjhis_Outpdoct_station（门诊医生工作站）
修复日期：2025年8月27日
修复原则：最小化修改，确保安全，可随时回滚

===============================================================================
一、修改内容总结
===============================================================================

1. 代码修改（2处关键修改）
   文件：TjhisPlatSource\Tjhis_Outpdoct_station\Business\OrderBusiness.cs
   
   修改点1（第1086行）：
   原代码：Costs = groupResult.Sum(s => s.COSTS)
   新代码：Costs = groupResult.Sum(s => s.CHARGES)
   
   修改点2（第1115-1116行）：
   原代码：decimal totals = orderCosts.Sum(s => s.COSTS)
   新代码：decimal totals = orderCosts.Sum(s => s.CHARGES)
   
   修改理由：
   - COSTS是成本价（医院进货价）
   - CHARGES是收费价（患者支付价）
   - 显示给患者的必须是CHARGES而非COSTS

2. 新增数据验证方法（不影响原有逻辑）
   位置：OrderBusiness.cs文件末尾（第2635-2704行）
   方法：ValidateCostCalculation()
   功能：检测异常费用计算，记录日志但不阻止业务

3. 数据修复SQL脚本
   文件：门诊费用数据修复SQL_20250827.sql
   特点：
   - 三重验证条件，精确识别错误数据
   - 分步执行，每步可验证
   - 包含回滚脚本

===============================================================================
二、实施步骤（必须严格按顺序执行）
===============================================================================

【第1步】备份现有代码（立即执行）
----------------------------------------
1. 备份OrderBusiness.cs文件：
   复制到：OrderBusiness.cs.backup_20250827

2. 记录当前版本信息：
   文件大小、修改时间、MD5值

【第2步】部署代码修改（高峰期外执行）
----------------------------------------
1. 修改OrderBusiness.cs的两处代码（已完成）
2. 添加ValidateCostCalculation方法（已完成）
3. 编译项目，确保无错误
4. 部署到测试环境

【第3步】测试验证（必须通过）
----------------------------------------
测试场景：
1. 开具中药处方，验证费用显示
2. 开具多种类药品，验证分类汇总
3. 查看费用明细，验证数值准确性

验证要点：
- 费用显示数学逻辑正确
- 分类费用之和 = 总费用
- CHARGES作为收费依据

【第4步】数据备份（生产环境）
----------------------------------------
执行SQL脚本第一步：
```sql
CREATE TABLE OUTP_ORDERS_COSTS_BACKUP_20250827 AS 
SELECT * FROM OUTP_ORDERS_COSTS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30;

CREATE TABLE OUTP_ORDERS_STANDARD_BACKUP_20250827 AS 
SELECT * FROM OUTP_ORDERS_STANDARD 
WHERE VISIT_DATE >= TRUNC(SYSDATE) - 30;
```

【第5步】数据分析（不修改数据）
----------------------------------------
执行SQL脚本第二步：
- 查询错误数据统计
- 生成待修复清单
- 人工审核确认

【第6步】生产部署（业务低峰期）
----------------------------------------
建议时间：凌晨2:00-4:00
1. 部署修改后的代码
2. 重启相关服务
3. 立即验证功能

【第7步】数据修复（分批执行）
----------------------------------------
1. 先修复最近7天数据
2. 验证无误后修复30天数据
3. 每批次后验证结果

【第8步】结果验证
----------------------------------------
执行SQL脚本第五步：
- 验证无错误记录
- 验证费用汇总一致
- 生成修复报告

===============================================================================
三、监控和验证
===============================================================================

1. 日志监控位置
   ..\Client\LOG\exLOG\门诊医生站_费用计算_YYYYMMDD.log
   ..\Client\LOG\exLOG\门诊医生站_费用异常_YYYYMMDD.log

2. 关键指标监控
   - 费用计算异常数量
   - CHARGES与COSTS的关系
   - 费用倍数异常（>10倍）

3. 业务验证点
   - 新开处方费用正确
   - 历史处方显示正常
   - 收费金额准确

===============================================================================
四、回滚方案（紧急情况）
===============================================================================

【代码回滚】
1. 恢复OrderBusiness.cs.backup_20250827
2. 重新编译部署
3. 重启服务

【数据回滚】
执行回滚SQL（已包含在脚本中）：
```sql
UPDATE OUTP_ORDERS_COSTS_STANDARD C
SET (COSTS, CHARGES) = (
    SELECT B.COSTS, B.CHARGES
    FROM OUTP_ORDERS_COSTS_BACKUP_20250827 B
    WHERE B.CLINIC_NO = C.CLINIC_NO
      AND B.ORDER_NO = C.ORDER_NO
)
WHERE EXISTS (
    SELECT 1
    FROM OUTP_ORDERS_COSTS_BACKUP_20250827 B
    WHERE B.CLINIC_NO = C.CLINIC_NO
);
```

===============================================================================
五、注意事项和风险控制
===============================================================================

1. 风险评估
   - 代码修改风险：低（仅改动2行显示逻辑）
   - 数据修复风险：中（需要仔细验证）
   - 业务影响：费用显示更准确

2. 特别注意
   - 不要在业务高峰期部署
   - 必须先备份再修改
   - 数据修复要分批进行
   - 保留所有操作日志

3. 紧急联系
   - 如遇异常立即回滚
   - 通知相关技术负责人
   - 记录问题详情

===============================================================================
六、验证检查清单
===============================================================================

□ 代码备份完成
□ 测试环境验证通过
□ 数据备份完成
□ 错误数据分析完成
□ 生产部署成功
□ 功能验证正常
□ 数据修复第一批完成
□ 数据修复全部完成
□ 最终验证通过
□ 监控正常运行

===============================================================================
七、后续优化建议
===============================================================================

1. 建立长期监控机制
   - 每日自动检查费用异常
   - 设置告警阈值
   - 定期生成报表

2. 代码优化
   - 增加更多数据验证
   - 优化日志记录
   - 完善异常处理

3. 流程改进
   - 建立费用计算测试用例
   - 加强代码审查
   - 定期数据质量检查

===============================================================================
文档结束 - 请严格按照步骤执行
===============================================================================