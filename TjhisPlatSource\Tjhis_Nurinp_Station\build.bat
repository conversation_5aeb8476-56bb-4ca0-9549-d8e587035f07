@echo off
echo 开始编译 Tjhis_Nurinp_Station 项目...

REM 设置MSBuild路径
set MSBUILD_PATH="C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\MSBuild\15.0\Bin\MSBuild.exe"

REM 检查MSBuild是否存在
if not exist %MSBUILD_PATH% (
    echo 错误：找不到MSBuild.exe，请检查Visual Studio 2017是否正确安装
    pause
    exit /b 1
)

REM 编译项目
echo 正在编译项目...
%MSBUILD_PATH% "Tjhis_Nurinp_Station.csproj" /p:Configuration=Debug /p:Platform="Any CPU" /verbosity:minimal

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！
) else (
    echo 编译失败，错误代码：%ERRORLEVEL%
)

pause
