using System;
using System.Collections;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace Tjhis.Nurinp.Station.Service
{
    /// <summary>
    /// 简单的菜单助手类
    /// </summary>
    public static class MenuHelper
    {
        private static NM_Service.NMService.ServerPublicClient ws = new NM_Service.NMService.ServerPublicClient();
        
        /// <summary>
        /// 添加护理记录单到日常工作菜单
        /// </summary>
        public static void AddNursingRecordMenu()
        {
            try
            {
                ArrayList sqlList = new ArrayList();
                
                // 创建日常工作主菜单
                sqlList.Add(@"
                    INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
                    SELECT 'NURINP', 'NURINP_MAIN05', 'MainWindow', '', 500, '日常工作', '', 'parent', '', null, '1', '2', '', '', '住院护理主菜单', '', ''
                    WHERE NOT EXISTS (SELECT 1 FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN05')");
                
                // 添加护理记录单菜单项
                sqlList.Add(@"
                    INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
                    SELECT 'NURINP', 'NURINP_MAIN0570', 'MainWindow', '', 570, '护理记录单', '', 'NURINP_MAIN05', 'TjhisAppPatientView.NursingRecordSheet.frmNursingRecord', null, '1', '1', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '住院护理主菜单', '', 'TjhisAppPatientView.dll'
                    WHERE NOT EXISTS (SELECT 1 FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN0570')");
                
                // 执行SQL
                ws.SaveTablesData(sqlList);
                
                XtraMessageBox.Show("菜单添加成功！请重新启动应用程序查看效果。", "成功", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"添加菜单失败：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
