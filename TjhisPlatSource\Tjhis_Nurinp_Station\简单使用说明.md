# 护理记录单菜单添加 - 简单使用说明

## 目标
将床头卡右键菜单中的"护理记录单"添加到顶部菜单"日常工作"中。

## 方法一：直接执行SQL（推荐）

1. **连接Oracle数据库**
2. **执行SQL脚本**：`AddNursingRecordMenu.sql`
3. **重启应用程序**

就这么简单！

## 方法二：使用代码工具

1. **以管理员身份登录**住院护理站
2. **打开床头卡**
3. **点击工具栏上的"添加护理记录单菜单"按钮**
4. **重启应用程序**

## 验证结果

重启后，在顶部菜单栏应该能看到：
- **日常工作** 菜单
- 点击后显示 **护理记录单** 子菜单项

## 技术说明

### 添加的菜单项：

**主菜单：**
- 菜单名称：日常工作
- 菜单代码：NURINP_MAIN05
- 序号：500

**子菜单：**
- 菜单名称：护理记录单  
- 菜单代码：NURINP_MAIN0570
- 序号：570
- 调用窗体：TjhisAppPatientView.NursingRecordSheet.frmNursingRecord
- 程序集：TjhisAppPatientView.dll

### 数据库表：
`COMM.SEC_MENUS_DICT`

## 回滚方法

如需删除添加的菜单：

```sql
DELETE FROM COMM.SEC_MENUS_DICT 
WHERE APPLICATION_CODE = 'NURINP' 
AND MENU_NAME IN ('NURINP_MAIN05', 'NURINP_MAIN0570');
COMMIT;
```

## 注意事项

1. **备份数据库**：执行前建议备份相关表
2. **重启应用**：添加菜单后必须重启应用程序
3. **权限检查**：确保用户有访问新菜单的权限
4. **测试验证**：在测试环境先验证功能正常

---

**就这么简单，不需要复杂的代码！**
