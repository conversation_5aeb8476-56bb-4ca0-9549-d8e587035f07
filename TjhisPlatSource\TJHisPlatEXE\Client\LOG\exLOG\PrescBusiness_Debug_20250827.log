[2025-08-27 12:35:56.948] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8023, 剂数: 7
[2025-08-27 12:35:56.948] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 15
[2025-08-27 12:35:56.949] [CDrugPresc.RepetitionChanged] 处理药品: 藿香, 处方号: 8023
[2025-08-27 12:35:56.951] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 藿香, 药品代码: 63080284YP1, 进入时dosage: 10g
[2025-08-27 12:35:56.951] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 藿香, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.017] [CDrugPresc.RepetitionChanged] 处理药品: 豆蔻, 处方号: 8023
[2025-08-27 12:35:57.018] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 豆蔻, 药品代码: 63080201YP1, 进入时dosage: 10g
[2025-08-27 12:35:57.018] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 豆蔻, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.074] [CDrugPresc.RepetitionChanged] 处理药品: 瓜蒌皮, 处方号: 8023
[2025-08-27 12:35:57.075] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 瓜蒌皮, 药品代码: 63080234YP1, 进入时dosage: 24g
[2025-08-27 12:35:57.075] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 瓜蒌皮, 保持原始剂量: 24g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.151] [CDrugPresc.RepetitionChanged] 处理药品: 黄芩片, 处方号: 8023
[2025-08-27 12:35:57.151] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 黄芩片, 药品代码: 63020007YP1, 进入时dosage: 10g
[2025-08-27 12:35:57.151] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 黄芩片, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.227] [CDrugPresc.RepetitionChanged] 处理药品: 黄连片, 处方号: 8023
[2025-08-27 12:35:57.227] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 黄连片, 药品代码: 63080269YP1, 进入时dosage: 3g
[2025-08-27 12:35:57.227] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 黄连片, 保持原始剂量: 3g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.304] [CDrugPresc.RepetitionChanged] 处理药品: 玉竹, 处方号: 8023
[2025-08-27 12:35:57.305] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 玉竹, 药品代码: 63080183YP1, 进入时dosage: 30g
[2025-08-27 12:35:57.305] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 玉竹, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.381] [CDrugPresc.RepetitionChanged] 处理药品: 太子参, 处方号: 8023
[2025-08-27 12:35:57.382] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 太子参, 药品代码: 63080222YP1, 进入时dosage: 30g
[2025-08-27 12:35:57.382] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 太子参, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.462] [CDrugPresc.RepetitionChanged] 处理药品: 炒麦芽, 处方号: 8023
[2025-08-27 12:35:57.463] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炒麦芽, 药品代码: 63080255YP1, 进入时dosage: 30g
[2025-08-27 12:35:57.463] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炒麦芽, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.526] [CDrugPresc.RepetitionChanged] 处理药品: 沉香曲, 处方号: 8023
[2025-08-27 12:35:57.527] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 沉香曲, 药品代码: 63080279YP1, 进入时dosage: 3g
[2025-08-27 12:35:57.527] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 沉香曲, 保持原始剂量: 3g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.589] [CDrugPresc.RepetitionChanged] 处理药品: 炒山楂, 处方号: 8023
[2025-08-27 12:35:57.589] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炒山楂, 药品代码: 63080214YP1, 进入时dosage: 30g
[2025-08-27 12:35:57.590] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炒山楂, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.650] [CDrugPresc.RepetitionChanged] 处理药品: 炙甘草, 处方号: 8023
[2025-08-27 12:35:57.650] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炙甘草, 药品代码: 63090004YP1, 进入时dosage: 6g
[2025-08-27 12:35:57.650] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炙甘草, 保持原始剂量: 6g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.721] [CDrugPresc.RepetitionChanged] 处理药品: 紫苏梗, 处方号: 8023
[2025-08-27 12:35:57.724] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 紫苏梗, 药品代码: 63080196YP1, 进入时dosage: 30g
[2025-08-27 12:35:57.724] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 紫苏梗, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.789] [CDrugPresc.RepetitionChanged] 处理药品: 木香, 处方号: 8023
[2025-08-27 12:35:57.789] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 木香, 药品代码: 63080233YP1, 进入时dosage: 10g
[2025-08-27 12:35:57.790] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 木香, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.850] [CDrugPresc.RepetitionChanged] 处理药品: 合欢皮, 处方号: 8023
[2025-08-27 12:35:57.851] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 合欢皮, 药品代码: 63080065YP1, 进入时dosage: 30g
[2025-08-27 12:35:57.851] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 合欢皮, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.913] [CDrugPresc.RepetitionChanged] 处理药品: 姜半夏, 处方号: 8023
[2025-08-27 12:35:57.914] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 姜半夏, 药品代码: 63080286YP1, 进入时dosage: 10g
[2025-08-27 12:35:57.914] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 姜半夏, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 12:35:57.977] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-27 13:21:47.054] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8023, 剂数: 7
[2025-08-27 13:21:47.055] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 15
[2025-08-27 13:21:47.055] [CDrugPresc.RepetitionChanged] 处理药品: 藿香, 处方号: 8023
[2025-08-27 13:21:47.057] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 藿香, 药品代码: 63080284YP1, 进入时dosage: 10g
[2025-08-27 13:21:47.058] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 藿香, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.118] [CDrugPresc.RepetitionChanged] 处理药品: 豆蔻, 处方号: 8023
[2025-08-27 13:21:47.118] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 豆蔻, 药品代码: 63080201YP1, 进入时dosage: 10g
[2025-08-27 13:21:47.118] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 豆蔻, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.177] [CDrugPresc.RepetitionChanged] 处理药品: 瓜蒌皮, 处方号: 8023
[2025-08-27 13:21:47.177] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 瓜蒌皮, 药品代码: 63080234YP1, 进入时dosage: 24g
[2025-08-27 13:21:47.177] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 瓜蒌皮, 保持原始剂量: 24g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.237] [CDrugPresc.RepetitionChanged] 处理药品: 黄芩片, 处方号: 8023
[2025-08-27 13:21:47.238] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 黄芩片, 药品代码: 63020007YP1, 进入时dosage: 10g
[2025-08-27 13:21:47.238] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 黄芩片, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.354] [CDrugPresc.RepetitionChanged] 处理药品: 黄连片, 处方号: 8023
[2025-08-27 13:21:47.354] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 黄连片, 药品代码: 63080269YP1, 进入时dosage: 3g
[2025-08-27 13:21:47.355] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 黄连片, 保持原始剂量: 3g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.448] [CDrugPresc.RepetitionChanged] 处理药品: 玉竹, 处方号: 8023
[2025-08-27 13:21:47.449] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 玉竹, 药品代码: 63080183YP1, 进入时dosage: 30g
[2025-08-27 13:21:47.449] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 玉竹, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.524] [CDrugPresc.RepetitionChanged] 处理药品: 太子参, 处方号: 8023
[2025-08-27 13:21:47.524] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 太子参, 药品代码: 63080222YP1, 进入时dosage: 30g
[2025-08-27 13:21:47.524] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 太子参, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.608] [CDrugPresc.RepetitionChanged] 处理药品: 炒麦芽, 处方号: 8023
[2025-08-27 13:21:47.609] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炒麦芽, 药品代码: 63080255YP1, 进入时dosage: 30g
[2025-08-27 13:21:47.609] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炒麦芽, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.666] [CDrugPresc.RepetitionChanged] 处理药品: 沉香曲, 处方号: 8023
[2025-08-27 13:21:47.666] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 沉香曲, 药品代码: 63080279YP1, 进入时dosage: 3g
[2025-08-27 13:21:47.666] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 沉香曲, 保持原始剂量: 3g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.721] [CDrugPresc.RepetitionChanged] 处理药品: 炒山楂, 处方号: 8023
[2025-08-27 13:21:47.721] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炒山楂, 药品代码: 63080214YP1, 进入时dosage: 30g
[2025-08-27 13:21:47.721] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炒山楂, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.778] [CDrugPresc.RepetitionChanged] 处理药品: 炙甘草, 处方号: 8023
[2025-08-27 13:21:47.778] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炙甘草, 药品代码: 63090004YP1, 进入时dosage: 6g
[2025-08-27 13:21:47.778] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炙甘草, 保持原始剂量: 6g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.837] [CDrugPresc.RepetitionChanged] 处理药品: 紫苏梗, 处方号: 8023
[2025-08-27 13:21:47.838] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 紫苏梗, 药品代码: 63080196YP1, 进入时dosage: 30g
[2025-08-27 13:21:47.838] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 紫苏梗, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.896] [CDrugPresc.RepetitionChanged] 处理药品: 木香, 处方号: 8023
[2025-08-27 13:21:47.896] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 木香, 药品代码: 63080233YP1, 进入时dosage: 10g
[2025-08-27 13:21:47.897] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 木香, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:47.969] [CDrugPresc.RepetitionChanged] 处理药品: 合欢皮, 处方号: 8023
[2025-08-27 13:21:47.970] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 合欢皮, 药品代码: 63080065YP1, 进入时dosage: 30g
[2025-08-27 13:21:47.970] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 合欢皮, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:48.028] [CDrugPresc.RepetitionChanged] 处理药品: 姜半夏, 处方号: 8023
[2025-08-27 13:21:48.030] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 姜半夏, 药品代码: 63080286YP1, 进入时dosage: 10g
[2025-08-27 13:21:48.030] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 姜半夏, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 13:21:48.090] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
[2025-08-27 16:22:57.769] [CDrugPresc.RepetitionChanged] 开始处理剂数变化 - 处方号: 8023, 剂数: 7
[2025-08-27 16:22:57.770] [CDrugPresc.RepetitionChanged] 找到当前处方药品数量: 15
[2025-08-27 16:22:57.773] [CDrugPresc.RepetitionChanged] 处理药品: 藿香, 处方号: 8023
[2025-08-27 16:22:57.775] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 藿香, 药品代码: 63080284YP1, 进入时dosage: 10g
[2025-08-27 16:22:57.776] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 藿香, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:57.827] [CDrugPresc.RepetitionChanged] 处理药品: 豆蔻, 处方号: 8023
[2025-08-27 16:22:57.828] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 豆蔻, 药品代码: 63080201YP1, 进入时dosage: 10g
[2025-08-27 16:22:57.828] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 豆蔻, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:57.895] [CDrugPresc.RepetitionChanged] 处理药品: 瓜蒌皮, 处方号: 8023
[2025-08-27 16:22:57.895] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 瓜蒌皮, 药品代码: 63080234YP1, 进入时dosage: 24g
[2025-08-27 16:22:57.896] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 瓜蒌皮, 保持原始剂量: 24g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:57.950] [CDrugPresc.RepetitionChanged] 处理药品: 黄芩片, 处方号: 8023
[2025-08-27 16:22:57.951] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 黄芩片, 药品代码: 63020007YP1, 进入时dosage: 10g
[2025-08-27 16:22:57.951] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 黄芩片, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.047] [CDrugPresc.RepetitionChanged] 处理药品: 黄连片, 处方号: 8023
[2025-08-27 16:22:58.047] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 黄连片, 药品代码: 63080269YP1, 进入时dosage: 3g
[2025-08-27 16:22:58.047] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 黄连片, 保持原始剂量: 3g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.121] [CDrugPresc.RepetitionChanged] 处理药品: 玉竹, 处方号: 8023
[2025-08-27 16:22:58.122] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 玉竹, 药品代码: 63080183YP1, 进入时dosage: 30g
[2025-08-27 16:22:58.122] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 玉竹, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.201] [CDrugPresc.RepetitionChanged] 处理药品: 太子参, 处方号: 8023
[2025-08-27 16:22:58.202] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 太子参, 药品代码: 63080222YP1, 进入时dosage: 30g
[2025-08-27 16:22:58.202] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 太子参, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.275] [CDrugPresc.RepetitionChanged] 处理药品: 炒麦芽, 处方号: 8023
[2025-08-27 16:22:58.276] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炒麦芽, 药品代码: 63080255YP1, 进入时dosage: 30g
[2025-08-27 16:22:58.276] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炒麦芽, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.332] [CDrugPresc.RepetitionChanged] 处理药品: 沉香曲, 处方号: 8023
[2025-08-27 16:22:58.333] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 沉香曲, 药品代码: 63080279YP1, 进入时dosage: 3g
[2025-08-27 16:22:58.333] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 沉香曲, 保持原始剂量: 3g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.390] [CDrugPresc.RepetitionChanged] 处理药品: 炒山楂, 处方号: 8023
[2025-08-27 16:22:58.390] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炒山楂, 药品代码: 63080214YP1, 进入时dosage: 30g
[2025-08-27 16:22:58.390] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炒山楂, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.444] [CDrugPresc.RepetitionChanged] 处理药品: 炙甘草, 处方号: 8023
[2025-08-27 16:22:58.445] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 炙甘草, 药品代码: 63090004YP1, 进入时dosage: 6g
[2025-08-27 16:22:58.445] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 炙甘草, 保持原始剂量: 6g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.504] [CDrugPresc.RepetitionChanged] 处理药品: 紫苏梗, 处方号: 8023
[2025-08-27 16:22:58.504] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 紫苏梗, 药品代码: 63080196YP1, 进入时dosage: 30g
[2025-08-27 16:22:58.505] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 紫苏梗, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.576] [CDrugPresc.RepetitionChanged] 处理药品: 木香, 处方号: 8023
[2025-08-27 16:22:58.577] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 木香, 药品代码: 63080233YP1, 进入时dosage: 10g
[2025-08-27 16:22:58.577] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 木香, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.640] [CDrugPresc.RepetitionChanged] 处理药品: 合欢皮, 处方号: 8023
[2025-08-27 16:22:58.640] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 合欢皮, 药品代码: 63080065YP1, 进入时dosage: 30g
[2025-08-27 16:22:58.641] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 合欢皮, 保持原始剂量: 30g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.704] [CDrugPresc.RepetitionChanged] 处理药品: 姜半夏, 处方号: 8023
[2025-08-27 16:22:58.705] [CDrugPresc.SetAmout] 方法开始 - 药品名称: 姜半夏, 药品代码: 63080286YP1, 进入时dosage: 10g
[2025-08-27 16:22:58.705] [CDrugPresc.SetAmout] 检查点[保护历史处方剂量] - 药品名称: 姜半夏, 保持原始剂量: 10g, 跳过CalculateSplitDosage计算
[2025-08-27 16:22:58.761] [CDrugPresc.RepetitionChanged] 剂数变化处理完成
