using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Tjhis.Nurinp.Station.Service;

namespace Tjhis.Nurinp.Station.Tools
{
    /// <summary>
    /// 菜单更新助手类
    /// 提供快速调用菜单更新功能的静态方法
    /// </summary>
    public static class MenuUpdateHelper
    {
        /// <summary>
        /// 显示菜单更新工具窗体
        /// </summary>
        public static void ShowMenuUpdateTool()
        {
            try
            {
                var tool = new MenuUpdateTool();
                tool.ShowDialog();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"打开菜单更新工具失败：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 快速执行菜单更新（无界面）
        /// </summary>
        /// <returns>是否成功</returns>
        public static bool QuickUpdateMenu()
        {
            try
            {
                // 询问用户是否确认执行
                var result = XtraMessageBox.Show(
                    "确定要将"护理记录单"添加到顶部菜单"日常工作"中吗？\n\n" +
                    "此操作将：\n" +
                    "1. 创建"日常工作"主菜单（如果不存在）\n" +
                    "2. 在其下添加"护理记录单"菜单项\n" +
                    "3. 需要重新启动应用程序才能看到效果",
                    "确认操作",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question
                );

                if (result != DialogResult.Yes)
                {
                    return false;
                }

                // 执行更新
                bool success = SimpleMenuUpdater.AddNursingRecordToMainMenu();
                
                if (success)
                {
                    // 验证结果
                    string validationResult = SimpleMenuUpdater.ValidateMenu();
                    
                    XtraMessageBox.Show(
                        validationResult + "\n\n重要提示：请重新启动住院护理站应用程序以查看新菜单！",
                        "更新结果",
                        MessageBoxButtons.OK,
                        validationResult.StartsWith("✓") ? MessageBoxIcon.Information : MessageBoxIcon.Warning
                    );
                }

                return success;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"执行菜单更新失败：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// 验证菜单状态
        /// </summary>
        public static void CheckMenuStatus()
        {
            try
            {
                string validationResult = SimpleMenuUpdater.ValidateMenu();
                
                XtraMessageBox.Show(
                    validationResult,
                    "菜单状态检查",
                    MessageBoxButtons.OK,
                    validationResult.StartsWith("✓") ? MessageBoxIcon.Information : MessageBoxIcon.Warning
                );
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"检查菜单状态失败：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 在床头卡中添加菜单更新按钮的示例方法
        /// 可以在床头卡窗体中调用此方法来添加一个"菜单设置"按钮
        /// </summary>
        /// <param name="parentForm">父窗体</param>
        /// <param name="barManager">BarManager实例</param>
        public static void AddMenuUpdateButtonToBedSideCard(Form parentForm, DevExpress.XtraBars.BarManager barManager)
        {
            try
            {
                // 创建菜单更新按钮
                var menuUpdateButton = new DevExpress.XtraBars.BarButtonItem(barManager, "菜单设置");
                menuUpdateButton.Caption = "菜单设置";
                menuUpdateButton.Hint = "将护理记录单添加到日常工作菜单";
                
                // 添加点击事件
                menuUpdateButton.ItemClick += (sender, e) =>
                {
                    ShowMenuUpdateTool();
                };

                // 将按钮添加到工具栏（需要根据实际的工具栏名称调整）
                // 这里假设有一个名为 "bar1" 的工具栏
                if (barManager.Bars.Count > 0)
                {
                    barManager.Bars[0].ItemLinks.Add(menuUpdateButton);
                }
            }
            catch (Exception ex)
            {
                // 静默处理异常，不影响主功能
                System.Diagnostics.Debug.WriteLine($"添加菜单更新按钮失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 创建日志记录
        /// </summary>
        /// <param name="message">日志消息</param>
        /// <param name="isError">是否为错误日志</param>
        public static void WriteLog(string message, bool isError = false)
        {
            try
            {
                string logPath = System.IO.Path.Combine(
                    Application.StartupPath, 
                    "..", "Client", "LOG", "exLOG", 
                    $"MenuUpdate_{DateTime.Now:yyyyMMdd}.log"
                );

                string logEntry = $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {(isError ? "[ERROR]" : "[INFO]")} {message}\r\n";
                
                System.IO.Directory.CreateDirectory(System.IO.Path.GetDirectoryName(logPath));
                System.IO.File.AppendAllText(logPath, logEntry, System.Text.Encoding.UTF8);
            }
            catch
            {
                // 静默处理日志写入异常
            }
        }
    }
}
