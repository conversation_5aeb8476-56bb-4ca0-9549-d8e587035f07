# 住院医生站处方剂数费用计算BUG修复验证

## 问题描述
住院处方中药剂数的乘法算法问题：开4剂药，保存后只能收到1剂的钱。

## 修复范围
**仅修改住院医生站**：`TjhisPlatSource\Tjhis_Doctor_Station\Business\Presc\frmPrescMain.cs`

## 修复内容
1. **DetailComputeItemMoney方法**：添加详细的费用计算日志
2. **RecalculateCostBeforeSave方法**：保存前重新计算费用，确保剂数信息正确
3. **PrescSave方法**：在保存前调用费用重新计算

## 测试步骤

### 测试前准备
1. 确保日志目录存在：`..\Client\LOG\exLOG\`
2. 准备测试数据：选择一个草药处方进行测试

### 测试用例1：新建草药处方
**步骤**：
1. 打开住院医生站
2. 新建草药处方
3. 添加药品：如"麦冬 15g"
4. 设置剂数：4剂
5. 保存处方

**验证点**：
- 预期：总数量 = 15g × 4剂 = 60g
- 预期：总费用 = 60g × 单价
- 实际：查看界面显示和数据库保存的费用是否一致

### 测试用例2：修改现有处方剂数
**步骤**：
1. 打开已保存的草药处方
2. 修改剂数：从2剂改为5剂
3. 保存处方

**验证点**：
- 费用是否按新剂数重新计算
- 数据库中的费用是否正确更新

### 测试用例3：多药品草药处方
**步骤**：
1. 新建草药处方
2. 添加多个药品：
   - 麦冬 15g
   - 甘草 10g
   - 黄芪 20g
3. 设置剂数：3剂
4. 保存处方

**验证点**：
- 每个药品的数量都正确乘以剂数
- 总费用为所有药品费用之和

### 测试用例4：验证日志记录
**步骤**：
1. 执行上述测试后，检查日志文件：
   - `住院处方费用计算_YYYYMMDD.log`
   - `住院处方费用重新计算_YYYYMMDD.log`

**验证点**：
- 日志中记录的药品名称、单次剂量、剂数
- 重新计算的数量和费用
- 计算过程是否完整

### 测试用例5：西药处方不受影响
**步骤**：
1. 新建西药处方
2. 添加西药
3. 保存处方

**验证点**：
- 西药处方费用计算不受影响
- 不会调用草药的费用重新计算逻辑

## 预期修复效果

### 修复前问题
开4剂草药处方，保存后只收到1剂的费用

### 修复后效果
1. ✅ 开4剂草药处方，正确收取4剂的费用
2. ✅ 修改剂数后，费用能正确重新计算
3. ✅ 界面显示费用与数据库保存费用完全一致
4. ✅ 详细日志记录便于问题追踪和验证

## 日志文件说明

### 日志路径
`..\Client\LOG\exLOG\`

### 日志文件
1. **住院处方费用计算_YYYYMMDD.log** - 记录每次费用计算的详细过程
2. **住院处方费用重新计算_YYYYMMDD.log** - 记录保存前的费用重新计算过程

### 日志格式
```
[YYYY-MM-DD HH:mm:ss] [INFO] 住院处方费用计算 - 药品:麦冬, 单次剂量:15, 剂数:4, 总数量:60, 单价:0.43, 应收单价:0.43, 计算费用:25.8, 应收费用:25.8
```

## 注意事项
1. **修复范围**：此修复只影响草药处方（PRESC_TYPE = "1"）
2. **兼容性**：西药处方不受影响
3. **系统范围**：住院急诊站不受影响
4. **安全性**：异常处理完善，不会影响主流程
5. **性能**：日志记录失败不影响保存功能

## 回滚方案
如果修复出现问题，可以：
1. 注释掉 `RecalculateCostBeforeSave()` 调用
2. 删除 `RecalculateCostBeforeSave` 方法
3. 删除 `DetailComputeItemMoney` 中的日志记录代码
