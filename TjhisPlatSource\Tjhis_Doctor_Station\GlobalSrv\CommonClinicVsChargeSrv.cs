﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.IO;
using NM_Service.NMService;
using Tjhis.Doctor.Station.Base;
using Tjhis.Doctor.Station.Global;

namespace Tjhis.Doctor.Station.GlobalSrv
{
    public  class CommonClinicVsChargeSrv
    {
        Boolean bIsHisUnit = true;
        
        #region 日志记录
        /// <summary>
        /// 记录药品取价日志
        /// </summary>
        /// <param name="logType">日志类型：INFO/ERROR/DEBUG</param>
        /// <param name="module">模块名称</param>
        /// <param name="message">日志消息</param>
        private void WriteDrugPriceLog(string logType, string module, string message)
        {
            try
            {
                // 日志目录路径
                string logDir = @"..\Client\LOG\exLOG\";
                if (!Directory.Exists(logDir))
                {
                    Directory.CreateDirectory(logDir);
                }
                
                // 按日期创建日志文件
                string fileName = string.Format("DrugPrice_{0}.log", DateTime.Now.ToString("yyyyMMdd"));
                string filePath = Path.Combine(logDir, fileName);
                
                // 格式化日志内容
                string logContent = string.Format("[{0}] [{1}] [{2}] {3}{4}",
                    DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff"),
                    logType,
                    module,
                    message,
                    Environment.NewLine);
                    
                // 写入日志文件
                File.AppendAllText(filePath, logContent, Encoding.UTF8);
            }
            catch (Exception ex)
            {
                // 日志记录失败时不影响主流程
                System.Diagnostics.Debug.WriteLine("日志记录失败: " + ex.Message);
            }
        }
        #endregion
        
        #region 获取药品价表信息
        /// <summary>
        /// 获取药品价表信息
        /// </summary>
        /// <param name="drugCode">药品代码</param>
        /// <param name="drugFrimSpec">药品规格厂家</param>
        /// <returns>DataTable</returns>
        public  DataTable GetDrugPrice(ServerPublicClient dbHelper,string drugCode,  string drugFrimSpec)
        {
            string sql = "";
            if(drugFrimSpec == null || drugFrimSpec == "")//无规格厂家
            {
                if (bIsHisUnit)
                {
                    sql = string.Format(@"SELECT  DRUG_DICT.DOSE_PER_UNIT,
                                                  DRUG_PRICE_MASTER_LIST.UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                  DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                  DRUG_DICT.DOSE_UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                  DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                  DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                  NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                           FROM   DRUG_DICT,DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                          WHERE   DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                            AND   DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE ='{1}'  
                                            AND   (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                            AND   (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                            AND   ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                                  ", drugCode, PlatCommon.SysBase.SystemParm.HisUnitCode);
                }
                else
                {
                    sql = string.Format(@"SELECT  DRUG_DICT.DOSE_PER_UNIT,
                                                  DRUG_PRICE_MASTER_LIST.UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                  DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                  DRUG_DICT.DOSE_UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                  DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                  DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                  NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                           FROM   DRUG_DICT,DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                          WHERE   DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                            AND   (DRUG_PRICE_LIST.START_DATE <= SYSDATE
                                            AND   (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                            AND   ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                                  ", drugCode);
                }
                
            }
            else //按规格厂家查询
            {
                if (bIsHisUnit)
                {
                    sql = string.Format(@"SELECT  DRUG_DICT.DOSE_PER_UNIT,
                                                  DRUG_PRICE_MASTER_LIST.UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                  DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                  DRUG_DICT.DOSE_UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                  DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                  DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                  NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                           FROM   DRUG_DICT,DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                          WHERE   DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                            AND   DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE ='{2}'  
                                            AND   (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                            AND   (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                            AND   ROWNUM = 1
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_CODE = '{0}'
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_SPEC || DRUG_PRICE_DETAIL_LIST.FIRM_ID ='{1}'"
                                            , drugCode, drugFrimSpec, PlatCommon.SysBase.SystemParm.HisUnitCode);
                }
                else
                {
                    sql = string.Format(@"SELECT  DRUG_DICT.DOSE_PER_UNIT,
                                                  DRUG_PRICE_MASTER_LIST.UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                  DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                  DRUG_DICT.DOSE_UNITS,
                                                  DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                  DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                  DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                  NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                           FROM   DRUG_DICT,DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                          WHERE   DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                            AND   DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                            AND   DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                            AND   (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                            AND   (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                            AND   ROWNUM = 1
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_CODE = '{0}'
                                            AND   DRUG_PRICE_DETAIL_LIST.DRUG_SPEC || DRUG_PRICE_DETAIL_LIST.FIRM_ID ='{1}'"
                                            , drugCode, drugFrimSpec);
                }
                    
            }
            return CommonDataBase.SelectDataTable(dbHelper, sql); 
        }
        #endregion
        #region 获取价表信息
        /// <summary>
        /// 获取价表信息
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="itemCode">项目代码</param>
        /// <param name="itemSpec">规格</param>
        /// <returns>DataTable</returns>
        public  DataTable GetPrice(ServerPublicClient dbHelper, string itemClass, string itemCode, string itemSpec)
        {
            return CommonDataBase.SelectDataTable(dbHelper, GetPriceSql(itemClass, itemCode, itemSpec));
        }
        public DataTable GetPrice(ServerPublicClient dbHelper, string itemClass, string itemCode, string itemSpec, string firmId, string performedBy)
        {
            return CommonDataBase.SelectDataTable(dbHelper, GetPriceSql(itemClass, itemCode, itemSpec, firmId, performedBy));
        }
        /// <summary>
        /// 获取价表信息
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="itemCode">项目代码</param>
        /// <param name="itemSpec">规格</param>
        /// <param name="units">单位</param>
        /// <returns>DataTable</returns>
        public  DataTable GetPrice(ServerPublicClient dbHelper, string itemClass, string itemCode, string itemSpec, string units)
        {
            return CommonDataBase.SelectDataTable(dbHelper, GetPriceSql(itemClass, itemCode, itemSpec, units));
        }
        public DataTable GetPrice(ServerPublicClient dbHelper, string itemClass, string itemCode, string itemSpec, string units, string firmId, string performedBy)
        {
            return CommonDataBase.SelectDataTable(dbHelper, GetPriceSql(itemClass, itemCode, itemSpec, units,firmId,performedBy));
        }
        #endregion 
        #region 获取途径名称对应的计价项目
        /// <summary>
        /// 获取途径名称对应的计价项目
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="administration">途径</param>
        /// <returns>DataTable</returns>
        public  DataTable GetAdminNameClinicVsCharge(ServerPublicClient dbHelper, string itemClass, string administration)
        {
            return CommonDataBase.SelectDataTable(dbHelper, GetAdminNameClinicVsChargeSql(itemClass, administration)); 
        }
        #endregion
        #region 获取项目代码对应的计价项目
        /// <summary>
        /// 获取项目代码对应的计价项目
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="itemCode">项目代码</param>
        /// <returns>DataTable</returns>
        public  DataTable GetItemCodeClinicVsCharge(ServerPublicClient dbHelper, string itemClass, string itemCode)
        {
            return CommonDataBase.SelectDataTable(dbHelper, GetItemCodeClinicVsChargeSql(itemClass, itemCode));
        }
        #endregion
        #region 获取药品对应的计价项目（含途径对应的计价项目）
        /// <summary>
        /// 获取药品对应的计价项目（含途径对应的计价项目）
        /// </summary>
        /// <param name="drugClass">药品类别</param>
        /// <param name="drugCode">药品代码</param>
        /// <param name="drugSpecFrim">规格厂家</param>
        /// <param name="administration">途径</param>
        /// <returns>DataSet</returns>
        public  DataSet GetDrugClinicVsCharge(ServerPublicClient dbHelper, string drugClass, string drugCode,string drugSpecFrim, string administration,string performedBy)
        {
            Dictionary<string, string> dataList = new Dictionary<string, string>();
            dataList.Add("CLINIC_VS_CHARGE", GetItemCodeClinicVsChargeSql(drugClass, drugCode));
            dataList.Add("DRUG_PRICE", GetDrugPriceListSql(drugCode, drugSpecFrim, performedBy));//项目代码对应的计价项目 
            if (!string.IsNullOrEmpty(administration))
            {
                dataList.Add("ADMINISTRATION_NAME_VS_CHARGE", GetAdminNameClinicVsChargeSql("E", administration));
            }
            return CommonDataBase.SelectDataSet(dbHelper, dataList);
        }
        #endregion
        #region 获取药品对应的计价项目
        /// <summary>
        /// 获取药品对应的计价项目
        /// </summary>
        /// <param name="drugClass">药品类别</param>
        /// <param name="drugCode">药品代码</param>
        /// <returns>DataTable</returns>
        public  DataTable GetDrugClinicVsCharge(ServerPublicClient dbHelper, string drugClass, string drugCode)
        {
            return CommonDataBase.SelectDataTable (dbHelper, GetItemCodeClinicVsChargeSql(drugClass, drugCode)); 
        }
        /// <summary>
        /// 获取药品对应的计价项目
        /// </summary>
        /// <param name="drugClass">药品类别</param>
        /// <param name="drugCode">药品代码</param>
        /// <param name="drugSpecFrim">规格厂家</param>
        /// <returns>DataTable</returns>
        public  DataSet GetDrugClinicVsCharge(ServerPublicClient dbHelper, string drugClass, string drugCode, string drugSpecFrim,string performedBy)
        {
            Dictionary<string, string> dataList = new Dictionary<string, string>();
            dataList.Add("CLINIC_VS_CHARGE", GetItemCodeClinicVsChargeSql(drugClass, drugCode));
            dataList.Add("DRUG_PRICE", GetDrugPriceListSql(drugCode, drugSpecFrim, performedBy));//项目代码对应的计价项目 
            return CommonDataBase.SelectDataSet(dbHelper, dataList);
        }
        #endregion
        #region  公费用药目录项目对应的计价项目
        /// <summary>
        /// 公费用药目录项目对应的计价项目
        /// </summary>
        /// <param name="itemCode">项目代码</param>
        /// <returns>DataTable</returns>
        public  DataTable GetOfficialDrugCatalogVsCharge(ServerPublicClient dbHelper, string itemCode)
        {
            string sql = string.Format(@"SELECT    CLINIC_VS_CHARGE.CHARGE_ITEM_CODE,
                                                       PRICE_ITEM_NAME_DICT.ITEM_NAME
                                                FROM   CLINIC_VS_CHARGE,
                                                        CLINIC_ITEM_NAME_DICT,
                                                        PRICE_ITEM_NAME_DICT,
                                                        OFFICIAL_DRUG_CATALOG
                                                WHERE   (CLINIC_VS_CHARGE.CLINIC_ITEM_CLASS =
                                                            CLINIC_ITEM_NAME_DICT.ITEM_CLASS)
                                                        AND (CLINIC_VS_CHARGE.CLINIC_ITEM_CODE =
                                                                CLINIC_ITEM_NAME_DICT.ITEM_CODE)
                                                        AND (CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS =
                                                                PRICE_ITEM_NAME_DICT.ITEM_CLASS)
                                                        AND (CLINIC_VS_CHARGE.CHARGE_ITEM_CODE =
                                                                PRICE_ITEM_NAME_DICT.ITEM_CODE)
                                                        AND (PRICE_ITEM_NAME_DICT.STD_INDICATOR = 1)
                                                        AND (OFFICIAL_DRUG_CATALOG.DRUG_CODE =
                                                                CLINIC_VS_CHARGE.CLINIC_ITEM_CODE)
                                                        AND CLINIC_VS_CHARGE.CLINIC_ITEM_CODE = '{0}' ", itemCode);
            return CommonDataBase.SelectDataTable(dbHelper, sql);
        }
        #endregion
        #region 获取途径对应的计价项目sql
        /// <summary>
        /// 获取途径对应的计价项目sql
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="administration">途径名称</param>
        /// <returns>string</returns>
        public  string GetAdminNameClinicVsChargeSql(string itemClass, string administration)
        {
            if (bIsHisUnit)
            {
                return string.Format(@"SELECT       PRICE_ITEM_NAME_DICT.ITEM_NAME ,
                                                    CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS ,
                                                    CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC ,
                                                    CLINIC_VS_CHARGE.CLINIC_ITEM_CODE ,
                                                    CLINIC_VS_CHARGE.CHARGE_ITEM_CODE ,
                                                    PRICE_ITEM_NAME_DICT.ITEM_CODE ,
                                                    CLINIC_VS_CHARGE.UNITS ,
                                                    NVL(CLINIC_VS_CHARGE.AMOUNT,1) AS AMOUNT ,
                                                    CLINIC_VS_CHARGE.BACKBILL_RULE,
                                                    CURRENT_PRICE_LIST.PRICE, 
                                                    CURRENT_PRICE_LIST.PREFER_PRICE,
                                                    CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                                    CURRENT_PRICE_LIST.PERFORMED_BY
                                                    FROM CLINIC_VS_CHARGE ,
                                                    CLINIC_ITEM_NAME_DICT ,
                                                    PRICE_ITEM_NAME_DICT,
                                                    CURRENT_PRICE_LIST 
                                                WHERE ( CLINIC_VS_CHARGE.CLINIC_ITEM_CLASS = CLINIC_ITEM_NAME_DICT.ITEM_CLASS ) AND
                                                      ( CLINIC_VS_CHARGE.CLINIC_ITEM_CODE = CLINIC_ITEM_NAME_DICT.ITEM_CODE ) AND
                                                      ( CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS = PRICE_ITEM_NAME_DICT.ITEM_CLASS ) AND
                                                      ( CLINIC_VS_CHARGE.CHARGE_ITEM_CODE = PRICE_ITEM_NAME_DICT.ITEM_CODE ) AND
                                                      (CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS=CURRENT_PRICE_LIST.ITEM_CLASS(+)) AND
                                                      (CLINIC_VS_CHARGE.CHARGE_ITEM_CODE=CURRENT_PRICE_LIST.ITEM_CODE(+)) AND
                                                      (CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC=CURRENT_PRICE_LIST.ITEM_SPEC(+)) AND
                                                      (CLINIC_VS_CHARGE.UNITS=CURRENT_PRICE_LIST.UNITS(+)) AND
                                                      (CLINIC_VS_CHARGE.HIS_UNIT_CODE=CURRENT_PRICE_LIST.HIS_UNIT_CODE(+)) AND
                                                      ( ( CLINIC_ITEM_NAME_DICT.ITEM_NAME = '{1}' ) ) AND
                                                      ( PRICE_ITEM_NAME_DICT.STD_INDICATOR = 1 ) AND
                                                      ( CLINIC_ITEM_NAME_DICT.ITEM_CLASS = '{0}' ) AND
                                                     ( CLINIC_VS_CHARGE.HIS_UNIT_CODE='{2}' )", itemClass, administration, PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
            else
            {
                return string.Format(@"SELECT           PRICE_ITEM_NAME_DICT.ITEM_NAME ,
                                                    CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS ,
                                                    CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC ,
                                                    CLINIC_VS_CHARGE.CLINIC_ITEM_CODE ,
                                                    CLINIC_VS_CHARGE.CHARGE_ITEM_CODE ,
                                                    PRICE_ITEM_NAME_DICT.ITEM_CODE ,
                                                    CLINIC_VS_CHARGE.UNITS ,
                                                    NVL(CLINIC_VS_CHARGE.AMOUNT,1) AS AMOUNT ,
                                                    CLINIC_VS_CHARGE.BACKBILL_RULE,
                                                    CURRENT_PRICE_LIST.PRICE, 
                                                    CURRENT_PRICE_LIST.PREFER_PRICE,
                                                    CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                                    CURRENT_PRICE_LIST.PERFORMED_BY
                                                    FROM CLINIC_VS_CHARGE ,
                                                    CLINIC_ITEM_NAME_DICT ,
                                                    PRICE_ITEM_NAME_DICT,
                                                    CURRENT_PRICE_LIST 
                                                WHERE ( CLINIC_VS_CHARGE.CLINIC_ITEM_CLASS = CLINIC_ITEM_NAME_DICT.ITEM_CLASS ) AND
                                                      ( CLINIC_VS_CHARGE.CLINIC_ITEM_CODE = CLINIC_ITEM_NAME_DICT.ITEM_CODE ) AND
                                                      ( CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS = PRICE_ITEM_NAME_DICT.ITEM_CLASS ) AND
                                                      ( CLINIC_VS_CHARGE.CHARGE_ITEM_CODE = PRICE_ITEM_NAME_DICT.ITEM_CODE ) AND
                                                      (CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS=CURRENT_PRICE_LIST.ITEM_CLASS(+)) AND
                                                      (CLINIC_VS_CHARGE.CHARGE_ITEM_CODE=CURRENT_PRICE_LIST.ITEM_CODE(+)) AND
                                                      (CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC=CURRENT_PRICE_LIST.ITEM_SPEC(+)) AND
                                                      (CLINIC_VS_CHARGE.UNITS=CURRENT_PRICE_LIST.UNITS(+)) AND
                                                      ( ( CLINIC_ITEM_NAME_DICT.ITEM_NAME = '{1}' ) ) AND
                                                      ( PRICE_ITEM_NAME_DICT.STD_INDICATOR = 1 ) AND
                                                      ( CLINIC_ITEM_NAME_DICT.ITEM_CLASS = '{0}' )   ", itemClass, administration);
            }
               
        }
        #endregion 
        #region 获取项目代码对应的计价项目sql
        /// <summary>
        /// 获取项目代码对应的计价项目sql
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="itemCode">项目代码</param>
        /// <returns>string</returns>
        public  string GetItemCodeClinicVsChargeSql(string itemClass, string itemCode)
        {    
            if (bIsHisUnit)
            {
                return string.Format(@"SELECT    PRICE_ITEM_NAME_DICT.ITEM_NAME,
                                             CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS,
                                             CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC,
                                             CLINIC_VS_CHARGE.CLINIC_ITEM_CODE,
                                             CLINIC_VS_CHARGE.CHARGE_ITEM_CODE,
                                             PRICE_ITEM_NAME_DICT.ITEM_CODE,
                                             CLINIC_VS_CHARGE.UNITS,
                                             NVL(CLINIC_VS_CHARGE.AMOUNT,1) AS AMOUNT,
                                             CLINIC_VS_CHARGE.BACKBILL_RULE,
                                             CURRENT_PRICE_LIST.PRICE, 
                                             CURRENT_PRICE_LIST.PREFER_PRICE,
                                             CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                             CURRENT_PRICE_LIST.PERFORMED_BY
                                      FROM   CLINIC_VS_CHARGE,
                                             CLINIC_ITEM_NAME_DICT, 
                                             PRICE_ITEM_NAME_DICT,
                                             CURRENT_PRICE_LIST
                                    WHERE    (CLINIC_VS_CHARGE.CLINIC_ITEM_CLASS =  CLINIC_ITEM_NAME_DICT.ITEM_CLASS) AND 
                                             (CLINIC_VS_CHARGE.CLINIC_ITEM_CODE =  CLINIC_ITEM_NAME_DICT.ITEM_CODE) AND 
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS = PRICE_ITEM_NAME_DICT.ITEM_CLASS) AND 
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CODE = PRICE_ITEM_NAME_DICT.ITEM_CODE) AND 
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS=CURRENT_PRICE_LIST.ITEM_CLASS(+)) AND
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CODE=CURRENT_PRICE_LIST.ITEM_CODE(+)) AND
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC=CURRENT_PRICE_LIST.ITEM_SPEC(+)) AND
                                             (CLINIC_VS_CHARGE.UNITS=CURRENT_PRICE_LIST.UNITS(+)) AND
                                             (CLINIC_VS_CHARGE.HIS_UNIT_CODE=CURRENT_PRICE_LIST.HIS_UNIT_CODE(+)) AND
                                             (CLINIC_ITEM_NAME_DICT.ITEM_CLASS = '{0}') AND 
                                             (CLINIC_ITEM_NAME_DICT.ITEM_CODE = '{1}') AND 
                                             (CLINIC_ITEM_NAME_DICT.STD_INDICATOR = 1) AND 
                                             (PRICE_ITEM_NAME_DICT.STD_INDICATOR = 1)  AND
                                             (CLINIC_VS_CHARGE.HIS_UNIT_CODE ='{2}' )"
                                              , itemClass, itemCode, PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
            else
            {
                return string.Format(@"SELECT    PRICE_ITEM_NAME_DICT.ITEM_NAME,
                                             CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS,
                                             CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC,
                                             CLINIC_VS_CHARGE.CLINIC_ITEM_CODE,
                                             CLINIC_VS_CHARGE.CHARGE_ITEM_CODE,
                                             PRICE_ITEM_NAME_DICT.ITEM_CODE,
                                             CLINIC_VS_CHARGE.UNITS,
                                             NVL(CLINIC_VS_CHARGE.AMOUNT,1) AS AMOUNT,
                                             CLINIC_VS_CHARGE.BACKBILL_RULE,
                                             CURRENT_PRICE_LIST.PRICE, 
                                             CURRENT_PRICE_LIST.PREFER_PRICE,
                                             CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                             CURRENT_PRICE_LIST.PERFORMED_BY
                                      FROM   CLINIC_VS_CHARGE,
                                             CLINIC_ITEM_NAME_DICT, 
                                             PRICE_ITEM_NAME_DICT,
                                             CURRENT_PRICE_LIST
                                    WHERE    (CLINIC_VS_CHARGE.CLINIC_ITEM_CLASS =  CLINIC_ITEM_NAME_DICT.ITEM_CLASS) AND 
                                             (CLINIC_VS_CHARGE.CLINIC_ITEM_CODE =  CLINIC_ITEM_NAME_DICT.ITEM_CODE) AND 
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS = PRICE_ITEM_NAME_DICT.ITEM_CLASS) AND 
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CODE = PRICE_ITEM_NAME_DICT.ITEM_CODE) AND 
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CLASS=CURRENT_PRICE_LIST.ITEM_CLASS(+)) AND
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_CODE=CURRENT_PRICE_LIST.ITEM_CODE(+)) AND
                                             (CLINIC_VS_CHARGE.CHARGE_ITEM_SPEC=CURRENT_PRICE_LIST.ITEM_SPEC(+)) AND
                                             (CLINIC_VS_CHARGE.UNITS=CURRENT_PRICE_LIST.UNITS(+)) AND
                                             (CLINIC_ITEM_NAME_DICT.ITEM_CLASS = '{0}') AND 
                                             (CLINIC_ITEM_NAME_DICT.ITEM_CODE = '{1}') AND 
                                             (CLINIC_ITEM_NAME_DICT.STD_INDICATOR = 1) AND 
                                             (PRICE_ITEM_NAME_DICT.STD_INDICATOR = 1) "
                                              , itemClass, itemCode);
            }
                
        }
        #endregion
        #region 获取价表信息
        /// <summary>
        /// 获取价表信息
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="itemCode">项目代码</param>
        /// <param name="itemSpec">项目规格</param>
        /// <returns>string</returns>
        public  string GetPriceSql(string itemClass, string itemCode, string itemSpec)
        {
            if (bIsHisUnit)
            {
                return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY,
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND 
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE='{3}'"
                                         , itemClass, itemCode, itemSpec, PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
            else
            {
                return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}'  "
                                                         , itemClass, itemCode, itemSpec);
            }
                
               
        }

        public string GetPriceSql(string itemClass, string itemCode, string itemSpec, string firmId, string performedBy)
        {
            // 2025-08-27 修改：药品类（A类西药、B类中药）统一从库存表取价，与门诊系统保持一致
            // 不再判断 performedBy 是否为空，药品必须从库存表获取实时价格
            if ("AB".Contains(itemClass))
            {
                // 记录药品取价开始
                WriteDrugPriceLog("INFO", "取价SQL", string.Format(
                    "开始获取药品价格 - 类别:{0}, 代码:{1}, 规格:{2}, 药房:{3}",
                    itemClass, itemCode, itemSpec, performedBy));
                    
                // 如果没有指定药房，需要给出错误提示
                if (string.IsNullOrEmpty(performedBy))
                {
                    WriteDrugPriceLog("ERROR", "取价SQL", string.Format(
                        "药品取价失败 - 未指定药房信息! 药品代码:{0}, 规格:{1}",
                        itemCode, itemSpec));
                        
                    // 返回一个会导致无结果的查询，让调用方知道需要提供药房信息
                    return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           0 AS PRICE,
                                           0 AS PREFER_PRICE,
                                           0 AS FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY,
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   1=0 -- 药品必须指定药房才能取价"
                                             );
                }
                
                WriteDrugPriceLog("DEBUG", "取价SQL", 
                    "从库存表(DRUG_STOCK)获取药品零售价");
                    
                // 从库存表获取药品价格，要求库存大于0且供应标志为1
                return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           DRUG_STOCK.RETAIL_PRICE PRICE,
                                           DRUG_STOCK.RETAIL_PRICE PREFER_PRICE,
                                           DRUG_STOCK.RETAIL_PRICE FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY,
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE
                                    FROM   CURRENT_PRICE_LIST,DRUG_STOCK
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           DRUG_STOCK.DRUG_CODE = CURRENT_PRICE_LIST.ITEM_CODE AND
                                           DRUG_STOCK.DRUG_SPEC || DRUG_STOCK.FIRM_ID=CURRENT_PRICE_LIST.ITEM_SPEC AND
                                           DRUG_STOCK.storage='{4}' AND 
                                           DRUG_STOCK.SUPPLY_INDICATOR = 1 AND  -- 添加供应标志验证
                                           DRUG_STOCK.QUANTITY>0 AND
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND 
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE='{3}'"
                                             , itemClass, itemCode, itemSpec, PlatCommon.SysBase.SystemParm.HisUnitCode, performedBy);
            }
            else
            {
                if (bIsHisUnit)
                {
                    return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY,
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND 
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE='{3}'"
                                             , itemClass, itemCode, itemSpec, PlatCommon.SysBase.SystemParm.HisUnitCode);
                }
                else
                {
                    return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}'  "
                                                             , itemClass, itemCode, itemSpec);
                }
            }
            
        }
        /// <summary>
        /// 获取价表信息
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="itemCode">项目代码</param>
        /// <param name="itemSpec">项目规格</param>
        /// <param name="units">项目单位</param>
        /// <returns>string</returns>
        public  string GetPriceSql(string itemClass, string itemCode, string itemSpec,string units)
        {
            if (bIsHisUnit)
            {
                return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND
                                           CURRENT_PRICE_LIST.UNITS = '{3}'AND 
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE='{4}'"
                                           , itemClass, itemCode, itemSpec, units, PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
            else
            {
                return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND
                                           CURRENT_PRICE_LIST.UNITS = '{3}'"
                         , itemClass, itemCode, itemSpec, units);
            }  
        }

        public string GetPriceSql(string itemClass, string itemCode, string itemSpec, string units, string firmId, string performedBy)
        {
            // 2025-08-27 修改：药品类（A类西药、B类中药）统一从库存表取价，与门诊系统保持一致
            // 不再判断 performedBy 是否为空，药品必须从库存表获取实时价格
            if ("AB".Contains(itemClass))
            {
                // 记录药品取价开始
                WriteDrugPriceLog("INFO", "取价SQL(带单位)", string.Format(
                    "开始获取药品价格 - 类别:{0}, 代码:{1}, 规格:{2}, 单位:{3}, 药房:{4}",
                    itemClass, itemCode, itemSpec, units, performedBy));
                    
                // 如果没有指定药房，需要给出错误提示
                if (string.IsNullOrEmpty(performedBy))
                {
                    WriteDrugPriceLog("ERROR", "取价SQL(带单位)", string.Format(
                        "药品取价失败 - 未指定药房信息! 药品代码:{0}, 规格:{1}, 单位:{2}",
                        itemCode, itemSpec, units));
                        
                    // 返回一个会导致无结果的查询，让调用方知道需要提供药房信息
                    return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           0 AS PRICE,
                                           0 AS PREFER_PRICE,
                                           0 AS FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   1=0 -- 药品必须指定药房才能取价"
                                             );
                }
                
                WriteDrugPriceLog("DEBUG", "取价SQL(带单位)", 
                    "从库存表(DRUG_STOCK)获取药品零售价");
                    
                // 从库存表获取药品价格，要求库存大于0且供应标志为1
                return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           DRUG_STOCK.RETAIL_PRICE PRICE,
                                           DRUG_STOCK.RETAIL_PRICE PREFER_PRICE,
                                           DRUG_STOCK.RETAIL_PRICE FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST,DRUG_STOCK
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND
                                           DRUG_STOCK.package_SPEC || DRUG_STOCK.FIRM_ID=CURRENT_PRICE_LIST.ITEM_SPEC AND
                                           DRUG_STOCK.storage='{5}' AND 
                                           DRUG_STOCK.SUPPLY_INDICATOR = 1 AND  -- 添加供应标志验证
                                           DRUG_STOCK.QUANTITY>0 AND
                                           CURRENT_PRICE_LIST.UNITS = '{3}' AND  
                                           DRUG_STOCK.drug_code='{1}' AND
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE='{4}'"
                                               , itemClass, itemCode, itemSpec, units, PlatCommon.SysBase.SystemParm.HisUnitCode, performedBy);
            }
            else
            {
                if (bIsHisUnit)
                {
                    return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND
                                           CURRENT_PRICE_LIST.UNITS = '{3}'AND 
                                           CURRENT_PRICE_LIST.HIS_UNIT_CODE='{4}'"
                                               , itemClass, itemCode, itemSpec, units, PlatCommon.SysBase.SystemParm.HisUnitCode);
                }
                else
                {
                    return string.Format(@"SELECT  CURRENT_PRICE_LIST.ITEM_CLASS,
                                           CURRENT_PRICE_LIST.ITEM_CODE,
                                           CURRENT_PRICE_LIST.ITEM_SPEC,
                                           CURRENT_PRICE_LIST.UNITS,
                                           CURRENT_PRICE_LIST.PRICE,
                                           CURRENT_PRICE_LIST.PREFER_PRICE,
                                           CURRENT_PRICE_LIST.FOREIGNER_PRICE,
                                           CURRENT_PRICE_LIST.PERFORMED_BY
                                    FROM   CURRENT_PRICE_LIST
                                   WHERE   CURRENT_PRICE_LIST.ITEM_CLASS = '{0}' AND 
                                           CURRENT_PRICE_LIST.ITEM_CODE = '{1}' AND  
                                           CURRENT_PRICE_LIST.ITEM_SPEC = '{2}' AND
                                           CURRENT_PRICE_LIST.UNITS = '{3}'"
                             , itemClass, itemCode, itemSpec, units);
                }
            }
        }
        #endregion
        #region 获取收费特殊项目排斥字典
        /// <summary>
        /// 获取收费特殊项目排斥字典
        /// </summary>
        /// <param name="chargeType">项目类别</param>
        /// <param name="chargeItemClass">项目类别</param>
        /// <param name="chargeItemCode">项目代码</param>
        /// <param name="chargeItemSpec">项目规格</param>
        /// <returns>DataTable</returns>
        public  DataTable GetChargeSpecialExceptDictList(ServerPublicClient dbHelper, string chargeType,string chargeItemClass,string chargeItemCode,string chargeItemSpec)
        {
            string sql = string.Format(@"SELECT NVL(PROPORTION_NUMERATOR,0) AS PROPORTION_NUMERATOR,
                                            NVL(PROPORTION_DENOMINATOR,0) AS PROPORTION_DENOMINATOR ,
                                            NVL(FREE_LIMIT,0) AS  FREE_LIMIT
                                    FROM CHARGE_SPECIAL_EXCEPT_DICT
                                    WHERE CHARGE_TYPE='{0}' AND
                                            ITEM_CLASS='{1}' AND
                                            (ITEM_CODE='{2}' OR ITEM_CODE='*') AND
                                            (ITEM_SPEC='{3}' OR ITEM_SPEC='*')"
                                           , chargeType
                                           , chargeItemClass
                                           , chargeItemCode
                                           , chargeItemSpec);
            if (bIsHisUnit)
            {
                sql = string.Format(@"SELECT NVL(PROPORTION_NUMERATOR,0) AS PROPORTION_NUMERATOR,
                                            NVL(PROPORTION_DENOMINATOR,0) AS PROPORTION_DENOMINATOR ,
                                            NVL(FREE_LIMIT,0) AS  FREE_LIMIT
                                    FROM CHARGE_SPECIAL_EXCEPT_DICT
                                    WHERE CHARGE_TYPE='{0}' AND
                                            ITEM_CLASS='{1}' AND
                                            (ITEM_CODE='{2}' OR ITEM_CODE='*') AND
                                            (ITEM_SPEC='{3}' OR ITEM_SPEC='*') AND 
                                           HIS_UNIT_CODE='{4}'"
                                           , chargeType
                                           , chargeItemClass
                                           , chargeItemCode
                                           , chargeItemSpec
                                           , PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
                return CommonDataBase.SelectDataTable(dbHelper, sql);
        }
        #endregion
        #region 获取收费特殊项目字典
        /// <summary>
        /// 获取收费特殊项目字典
        /// </summary>
        /// <param name="chargeType">项目类别</param>
        /// <param name="chargeItemClass">项目类别</param>
        /// <param name="chargeItemCode">项目代码</param>
        /// <param name="chargeItemSpec">项目规格</param>
        /// <returns>DataTable</returns>
        public  DataTable GetChargeSpecialItemDictList(ServerPublicClient dbHelper, string chargeType, string chargeItemClass, string chargeItemCode, string chargeItemSpec)
        {
            string sql = "";
            if (bIsHisUnit)
            {
                sql = string.Format(@"SELECT PROPORTION_NUMERATOR,
                                             PROPORTION_DENOMINATOR,
                                             FREE_LIMIT 
                                        FROM CHARGE_SPECIAL_ITEM_DICT
                                       WHERE CHARGE_TYPE='{0}' AND
                                             ITEM_CLASS='{1}' AND
                                             (ITEM_CODE='{2}' OR ITEM_CODE='*') AND
                                             (ITEM_SPEC='{3}' OR ITEM_SPEC='*')AND 
                                             HIS_UNIT_CODE='{4}'"
                                            , chargeType
                                            , chargeItemClass
                                            , chargeItemCode
                                            , chargeItemSpec
                                            , PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
            else
            {
                sql = string.Format(@"SELECT PROPORTION_NUMERATOR,
                                             PROPORTION_DENOMINATOR,
                                             FREE_LIMIT 
                                        FROM CHARGE_SPECIAL_ITEM_DICT
                                       WHERE CHARGE_TYPE='{0}' AND
                                             ITEM_CLASS='{1}' AND
                                             (ITEM_CODE='{2}' OR ITEM_CODE='*') AND
                                             (ITEM_SPEC='{3}' OR ITEM_SPEC='*') "
                                          , chargeType
                                          , chargeItemClass
                                          , chargeItemCode
                                          , chargeItemSpec);
            }

            return CommonDataBase.SelectDataTable(dbHelper, sql);
        }
        #endregion
        #region 获取药品对应的计价项目sql
        /// <summary>
        /// 获取药品对应的计价项目sql
        /// </summary>
        /// <param name="itemCode">项目代码</param>
        /// <param name="itemFrimSpec">项目厂家规格</param>
        /// <returns>string</returns>
        public  string GetDrugPriceListSql(string itemCode, string itemFrimSpec)
        {
            string sql = "";
            if (string.IsNullOrEmpty(itemFrimSpec))//无规格厂家
            {
                if (bIsHisUnit)
                {
                    sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE = '{1}’
                                             ", itemCode, PlatCommon.SysBase.SystemParm.HisUnitCode);
                }
                else
                {
                    sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             ", itemCode);
                }
                    
            }
            else//按规格厂家查询
            {
                if (bIsHisUnit)
                {
                    sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_SPEC || DRUG_PRICE_DETAIL_LIST.FIRM_ID ='{1}'
                                             AND DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE = '{2}'"
                                             , itemCode, itemFrimSpec, PlatCommon.SysBase.SystemParm.HisUnitCode);
                }
                else
                {
                    sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_SPEC || DRUG_PRICE_DETAIL_LIST.FIRM_ID ='{1}'"
                                             , itemCode, itemFrimSpec);
                }
                    
            }
            return sql;
        }

        /// <summary>
        /// 获取药品对应的计价项目sql
        /// </summary>
        /// <param name="itemCode">项目代码</param>
        /// <param name="itemFrimSpec">项目厂家规格</param>
        /// <param name="performedBy">药房编码</param>
        /// <returns></returns>
        public string GetDrugPriceListSql(string itemCode, string itemFrimSpec,string performedBy)
        {
            string sql = "";
            if (string.IsNullOrEmpty(performedBy))
            {
                if (string.IsNullOrEmpty(itemFrimSpec))//无规格厂家
                {
                    if (bIsHisUnit)
                    {
                        sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE = '{1}’
                                             ", itemCode, PlatCommon.SysBase.SystemParm.HisUnitCode);
                    }
                    else
                    {
                        sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             ", itemCode);
                    }

                }
                else//按规格厂家查询
                {
                    if (bIsHisUnit)
                    {
                        sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_SPEC || DRUG_PRICE_DETAIL_LIST.FIRM_ID ='{1}'
                                             AND DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE = '{2}'"
                                                 , itemCode, itemFrimSpec, PlatCommon.SysBase.SystemParm.HisUnitCode);
                    }
                    else
                    {
                        sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_PRICE_DETAIL_LIST.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_SPEC || DRUG_PRICE_DETAIL_LIST.FIRM_ID ='{1}'"
                                                 , itemCode, itemFrimSpec);
                    }

                }
            }
            else
            {
                if (string.IsNullOrEmpty(itemFrimSpec))//无规格厂家
                {
                    if (bIsHisUnit)
                    {
                        sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_STOCK.RETAIL_PRICE AS PRICE,
                                                 DRUG_STOCK.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_STOCK.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST,DRUG_STOCK
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             and DRUG_STOCK.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_STOCK.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_STOCK.FIRM_ID = DRUG_PRICE_DETAIL_LIST.FIRM_ID
                                             and DRUG_STOCK.storage='{3}' and DRUG_STOCK.quantity>0
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE = '{1}’
                                             ", itemCode, PlatCommon.SysBase.SystemParm.HisUnitCode);
                    }

                }
                else//按规格厂家查询
                {
                    if (bIsHisUnit)
                    {
                        sql = string.Format(@"SELECT DRUG_PRICE_DETAIL_LIST.DRUG_CODE,
                                                 DRUG_DICT.DOSE_PER_UNIT,
                                                 DRUG_PRICE_MASTER_LIST.UNITS,
                                                 DRUG_PRICE_DETAIL_LIST.DRUG_SPEC,
                                                 DRUG_PRICE_DETAIL_LIST.FIRM_ID,
                                                 DRUG_DICT.DOSE_UNITS,
                                                 DRUG_STOCK.RETAIL_PRICE AS PRICE,
                                                 DRUG_STOCK.RETAIL_PRICE AS PREFER_PRICE,
                                                 DRUG_STOCK.RETAIL_PRICE AS FOREIGNER_PRICE,
                                                 DRUG_PRICE_MASTER_LIST.MIN_SPEC,
                                                 DRUG_PRICE_MASTER_LIST.MIN_UNITS,
                                                 NVL(DRUG_PRICE_MASTER_LIST.AMOUNT_PER_PACKAGE, 1) AS AMOUNT_PER_PACKAGE
                                            FROM DRUG_DICT, DRUG_PRICE_MASTER_LIST, DRUG_PRICE_DETAIL_LIST,DRUG_STOCK
                                           WHERE DRUG_PRICE_MASTER_LIST.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_NAME = DRUG_PRICE_DETAIL_LIST.DRUG_NAME
                                             AND DRUG_PRICE_MASTER_LIST.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE = DRUG_DICT.DRUG_CODE
                                             AND DRUG_PRICE_MASTER_LIST.MIN_SPEC = DRUG_DICT.DRUG_SPEC
                                             and DRUG_STOCK.DRUG_CODE = DRUG_PRICE_DETAIL_LIST.DRUG_CODE
                                             AND DRUG_STOCK.DRUG_SPEC = DRUG_PRICE_DETAIL_LIST.DRUG_SPEC
                                             AND DRUG_STOCK.FIRM_ID = DRUG_PRICE_DETAIL_LIST.FIRM_ID
                                             and DRUG_STOCK.storage='{3}' and DRUG_STOCK.quantity>0
                                             AND (DRUG_PRICE_DETAIL_LIST.START_DATE <= SYSDATE
                                             AND (DRUG_PRICE_DETAIL_LIST.stop_date IS NULL OR stop_date >= SYSDATE))
                                             AND ROWNUM = 1 AND DRUG_PRICE_DETAIL_LIST.DRUG_CODE ='{0}' 
                                             AND DRUG_PRICE_DETAIL_LIST.DRUG_SPEC || DRUG_PRICE_DETAIL_LIST.FIRM_ID ='{1}'
                                             AND DRUG_PRICE_DETAIL_LIST.HIS_UNIT_CODE = '{2}'"
                                                 , itemCode, itemFrimSpec, PlatCommon.SysBase.SystemParm.HisUnitCode, performedBy);
                    }
                }
            }
            
            return sql;
        }
        #endregion
        #region 获取诊疗项目
        /// <summary>
        /// 获取诊疗项目
        /// </summary>
        /// <param name="itemClass">项目类别</param>
        /// <param name="itemCode">项目代码</param>
        /// <returns>DataTable</returns>
        public  DataTable GetClinicItemDict(ServerPublicClient dbHelper, string itemClass,string itemCode)
        {
            string sql = string.Format(@"SELECT * 
                                           FROM CLINIC_ITEM_DICT
                                          WHERE ITEM_CLASS='{0}' AND
                                                ITEM_CODE='{1}'", itemClass, itemCode);
            if (bIsHisUnit)
            {
                sql = string.Format(@"SELECT * 
                                           FROM CLINIC_ITEM_DICT
                                          WHERE ITEM_CLASS='{0}' AND
                                                ITEM_CODE='{1}' AND 
                                                HIS_UNIT_CODE ='{2}'", itemClass, itemCode, PlatCommon.SysBase.SystemParm.HisUnitCode);
            }
            return CommonDataBase.SelectDataTable(dbHelper, sql, "CLINIC_ITEM_DICT");
        }
        #endregion 
    }
}
