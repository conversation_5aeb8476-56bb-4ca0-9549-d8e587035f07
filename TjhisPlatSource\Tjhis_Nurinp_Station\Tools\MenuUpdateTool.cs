using System;
using System.Data;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Tjhis.Nurinp.Station.Service;

namespace Tjhis.Nurinp.Station.Tools
{
    /// <summary>
    /// 菜单更新工具窗体
    /// 用于将床头卡右键菜单中的"护理记录单"添加到顶部菜单"日常工作"中
    /// </summary>
    public class MenuUpdateTool : XtraForm
    {
        private TextBox resultTextBox;

        public MenuUpdateTool()
        {
            InitializeUI();
        }

        private void InitializeUI()
        {
            this.Text = "护理记录单菜单更新工具";
            this.Size = new System.Drawing.Size(600, 400);
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建主面板
            var mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(10)
            };
            this.Controls.Add(mainPanel);

            // 标题标签
            var titleLabel = new Label
            {
                Text = "将护理记录单添加到日常工作菜单",
                Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.DarkBlue,
                AutoSize = true,
                Location = new System.Drawing.Point(10, 10)
            };
            mainPanel.Controls.Add(titleLabel);

            // 执行更新按钮
            var executeButton = new SimpleButton
            {
                Text = "执行菜单更新",
                Size = new System.Drawing.Size(120, 30),
                Location = new System.Drawing.Point(10, 50)
            };
            executeButton.Click += ExecuteButton_Click;
            mainPanel.Controls.Add(executeButton);

            // 关闭按钮
            var closeButton = new SimpleButton
            {
                Text = "关闭",
                Size = new System.Drawing.Size(80, 30),
                Location = new System.Drawing.Point(140, 50)
            };
            closeButton.Click += (s, e) => this.Close();
            mainPanel.Controls.Add(closeButton);

            // 结果文本框
            resultTextBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Location = new System.Drawing.Point(10, 90),
                Size = new System.Drawing.Size(560, 250),
                Font = new System.Drawing.Font("Consolas", 9F)
            };
            mainPanel.Controls.Add(resultTextBox);
        }

        private void ExecuteButton_Click(object sender, EventArgs e)
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                resultTextBox.Clear();
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 开始执行菜单更新...\r\n");

                // 执行更新
                bool success = SimpleMenuUpdater.AddNursingRecordToMainMenu();
                
                if (success)
                {
                    resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✓ 菜单更新SQL执行成功\r\n");
                    
                    // 验证结果
                    string validationResult = SimpleMenuUpdater.ValidateMenu();
                    resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 验证结果：\r\n{validationResult}\r\n");
                    
                    if (validationResult.StartsWith("✓"))
                    {
                        resultTextBox.AppendText($"\r\n重要提示：请重新启动住院护理站应用程序以查看新菜单！\r\n");
                        XtraMessageBox.Show("菜单更新成功！\n\n请重新启动住院护理站应用程序以查看新的"日常工作"菜单。", 
                            "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✗ 菜单更新失败\r\n");
                }
            }
            catch (Exception ex)
            {
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 异常：{ex.Message}\r\n");
                XtraMessageBox.Show($"执行过程中发生异常：{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }
    }
}
