using System;
using System.Data;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Tjhis.Nurinp.Station.Service;
using PlatCommon.SysBase;

namespace Tjhis.Nurinp.Station.Tools
{
    /// <summary>
    /// 菜单更新工具窗体
    /// 用于将床头卡右键菜单中的"护理记录单"添加到顶部菜单"日常工作"中
    /// </summary>
    public partial class MenuUpdateTool : XtraForm
    {
        public MenuUpdateTool()
        {
            InitializeComponent();
            InitializeUI();
        }

        private void InitializeUI()
        {
            this.Text = "护理记录单菜单更新工具";
            this.Size = new System.Drawing.Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            
            // 创建主面板
            var mainPanel = new System.Windows.Forms.Panel
            {
                Dock = DockStyle.Fill,
                Padding = new System.Windows.Forms.Padding(10)
            };
            this.Controls.Add(mainPanel);

            // 标题标签
            var titleLabel = new System.Windows.Forms.Label
            {
                Text = "将床头卡右键菜单中的"护理记录单"添加到顶部菜单"日常工作"中",
                Font = new System.Drawing.Font("微软雅黑", 12F, System.Drawing.FontStyle.Bold),
                ForeColor = System.Drawing.Color.DarkBlue,
                AutoSize = true,
                Location = new System.Drawing.Point(10, 10)
            };
            mainPanel.Controls.Add(titleLabel);

            // 说明文本
            var descriptionLabel = new System.Windows.Forms.Label
            {
                Text = @"此工具将执行以下操作：
1. 检查并创建"日常工作"主菜单（如果不存在）
2. 在"日常工作"菜单下添加"护理记录单"菜单项
3. 菜单项将调用与床头卡右键菜单相同的护理记录单窗体
4. 添加完成后需要重新启动应用程序才能看到效果",
                Font = new System.Drawing.Font("微软雅黑", 9F),
                AutoSize = false,
                Size = new System.Drawing.Size(760, 80),
                Location = new System.Drawing.Point(10, 50)
            };
            mainPanel.Controls.Add(descriptionLabel);

            // 按钮面板
            var buttonPanel = new System.Windows.Forms.Panel
            {
                Height = 50,
                Dock = DockStyle.Bottom
            };
            mainPanel.Controls.Add(buttonPanel);

            // 执行更新按钮
            var executeButton = new SimpleButton
            {
                Text = "执行菜单更新",
                Size = new System.Drawing.Size(120, 30),
                Location = new System.Drawing.Point(10, 10)
            };
            executeButton.Click += ExecuteButton_Click;
            buttonPanel.Controls.Add(executeButton);

            // 验证菜单按钮
            var validateButton = new SimpleButton
            {
                Text = "验证菜单状态",
                Size = new System.Drawing.Size(120, 30),
                Location = new System.Drawing.Point(140, 10)
            };
            validateButton.Click += ValidateButton_Click;
            buttonPanel.Controls.Add(validateButton);

            // 查看所有菜单按钮
            var viewAllButton = new SimpleButton
            {
                Text = "查看所有菜单",
                Size = new System.Drawing.Size(120, 30),
                Location = new System.Drawing.Point(270, 10)
            };
            viewAllButton.Click += ViewAllButton_Click;
            buttonPanel.Controls.Add(viewAllButton);

            // 关闭按钮
            var closeButton = new SimpleButton
            {
                Text = "关闭",
                Size = new System.Drawing.Size(80, 30),
                Location = new System.Drawing.Point(400, 10)
            };
            closeButton.Click += (s, e) => this.Close();
            buttonPanel.Controls.Add(closeButton);

            // 结果显示区域
            var resultPanel = new System.Windows.Forms.Panel
            {
                Location = new System.Drawing.Point(10, 140),
                Size = new System.Drawing.Size(760, 350),
                BorderStyle = BorderStyle.FixedSingle
            };
            mainPanel.Controls.Add(resultPanel);

            var resultLabel = new System.Windows.Forms.Label
            {
                Text = "操作结果：",
                Font = new System.Drawing.Font("微软雅黑", 9F, System.Drawing.FontStyle.Bold),
                Location = new System.Drawing.Point(5, 5),
                AutoSize = true
            };
            resultPanel.Controls.Add(resultLabel);

            // 结果文本框
            resultTextBox = new System.Windows.Forms.TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Location = new System.Drawing.Point(5, 25),
                Size = new System.Drawing.Size(748, 318),
                Font = new System.Drawing.Font("Consolas", 9F)
            };
            resultPanel.Controls.Add(resultTextBox);
        }

        private System.Windows.Forms.TextBox resultTextBox;

        private void ExecuteButton_Click(object sender, EventArgs e)
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                resultTextBox.Clear();
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 开始执行菜单更新...\r\n");

                // 执行更新
                bool success = SimpleMenuUpdater.AddNursingRecordToMainMenu();
                
                if (success)
                {
                    resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✓ 菜单更新SQL执行成功\r\n");
                    
                    // 验证结果
                    string validationResult = SimpleMenuUpdater.ValidateMenu();
                    resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 验证结果：\r\n{validationResult}\r\n");
                    
                    if (validationResult.StartsWith("✓"))
                    {
                        resultTextBox.AppendText($"\r\n重要提示：请重新启动住院护理站应用程序以查看新菜单！\r\n");
                        XtraMessageBox.Show("菜单更新成功！\n\n请重新启动住院护理站应用程序以查看新的"日常工作"菜单。", 
                            "成功", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] ✗ 菜单更新失败\r\n");
                }
            }
            catch (Exception ex)
            {
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 异常：{ex.Message}\r\n");
                XtraMessageBox.Show($"执行过程中发生异常：{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void ValidateButton_Click(object sender, EventArgs e)
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                resultTextBox.Clear();
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 开始验证菜单状态...\r\n");

                string validationResult = SimpleMenuUpdater.ValidateMenu();
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 验证结果：\r\n{validationResult}\r\n");
            }
            catch (Exception ex)
            {
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 验证异常：{ex.Message}\r\n");
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        private void ViewAllButton_Click(object sender, EventArgs e)
        {
            try
            {
                this.Cursor = Cursors.WaitCursor;
                resultTextBox.Clear();
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 获取所有NURINP菜单...\r\n");

                DataTable menuTable = SimpleMenuUpdater.GetAllNurinpMenus();
                if (menuTable != null && menuTable.Rows.Count > 0)
                {
                    resultTextBox.AppendText($"找到 {menuTable.Rows.Count} 个菜单项：\r\n\r\n");
                    resultTextBox.AppendText("序号\t菜单名称\t\t显示文本\t\t父菜单\t\t打开窗体\r\n");
                    resultTextBox.AppendText(new string('-', 100) + "\r\n");
                    
                    foreach (DataRow row in menuTable.Rows)
                    {
                        string serialNo = row["SERIAL_NO"]?.ToString() ?? "";
                        string menuName = row["MENU_NAME"]?.ToString() ?? "";
                        string menuText = row["MENU_TEXT"]?.ToString() ?? "";
                        string supperMenu = row["SUPPER_MENU"]?.ToString() ?? "";
                        string openForm = row["OPEN_FORM"]?.ToString() ?? "";
                        
                        resultTextBox.AppendText($"{serialNo}\t{menuName}\t{menuText}\t{supperMenu}\t{openForm}\r\n");
                    }
                }
                else
                {
                    resultTextBox.AppendText("未找到任何NURINP菜单项\r\n");
                }
            }
            catch (Exception ex)
            {
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 获取菜单异常：{ex.Message}\r\n");
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }
    }
}
