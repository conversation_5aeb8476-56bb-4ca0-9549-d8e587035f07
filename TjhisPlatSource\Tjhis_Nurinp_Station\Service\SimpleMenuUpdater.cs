using System;
using System.Collections;
using System.Data;
using System.Windows.Forms;

namespace Tjhis.Nurinp.Station.Service
{
    /// <summary>
    /// 简单的菜单更新器
    /// 用于将护理记录单添加到日常工作菜单中
    /// </summary>
    public static class SimpleMenuUpdater
    {
        private static NM_Service.NMService.ServerPublicClient ws = new NM_Service.NMService.ServerPublicClient();
        
        /// <summary>
        /// 添加护理记录单到日常工作菜单
        /// </summary>
        /// <returns>是否成功</returns>
        public static bool AddNursingRecordToMainMenu()
        {
            try
            {
                ArrayList sqlList = new ArrayList();
                
                // 1. 检查并创建日常工作主菜单（如果不存在）
                string createMainMenuSql = @"
                    INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
                    SELECT 'NURINP', 'NURINP_MAIN05', 'MainWindow', '', 500, '日常工作', '', 'parent', '', null, '1', '2', '', '', '住院护理主菜单', '', ''
                    WHERE NOT EXISTS (
                        SELECT 1 FROM COMM.SEC_MENUS_DICT 
                        WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN05'
                    )";
                
                sqlList.Add(createMainMenuSql);
                
                // 2. 添加护理记录单菜单项
                string addNursingRecordSql = @"
                    INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
                    SELECT 'NURINP', 'NURINP_MAIN0570', 'MainWindow', '', 570, '护理记录单', '', 'NURINP_MAIN05', 'TjhisAppPatientView.NursingRecordSheet.frmNursingRecord', null, '1', '1', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '住院护理主菜单', '', 'TjhisAppPatientView.dll'
                    WHERE NOT EXISTS (
                        SELECT 1 FROM COMM.SEC_MENUS_DICT 
                        WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN0570'
                    )";
                
                sqlList.Add(addNursingRecordSql);
                
                // 执行SQL
                ws.SaveTablesData(sqlList);
                
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"添加菜单失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }
        
        /// <summary>
        /// 验证菜单是否添加成功
        /// </summary>
        /// <returns>验证结果</returns>
        public static string ValidateMenu()
        {
            try
            {
                string checkSql = @"
                    SELECT 
                        APPLICATION_CODE,
                        MENU_NAME,
                        MENU_TEXT,
                        SERIAL_NO,
                        SUPPER_MENU,
                        OPEN_FORM
                    FROM COMM.SEC_MENUS_DICT 
                    WHERE APPLICATION_CODE = 'NURINP' 
                    AND (MENU_NAME = 'NURINP_MAIN05' OR MENU_NAME = 'NURINP_MAIN0570')
                    ORDER BY SERIAL_NO";
                
                DataSet result = ws.GetDataBySql(checkSql, "MENU_CHECK", false);
                
                if (result.Tables[0].Rows.Count == 0)
                {
                    return "未找到任何菜单项，请先执行添加操作";
                }
                
                bool hasMainMenu = false;
                bool hasNursingRecord = false;
                
                foreach (DataRow row in result.Tables[0].Rows)
                {
                    if (row["MENU_NAME"].ToString() == "NURINP_MAIN05")
                        hasMainMenu = true;
                    if (row["MENU_NAME"].ToString() == "NURINP_MAIN0570")
                        hasNursingRecord = true;
                }
                
                if (hasMainMenu && hasNursingRecord)
                {
                    return "✓ 菜单添加成功！日常工作菜单和护理记录单都已存在。\n请重新启动应用程序查看效果。";
                }
                else if (hasMainMenu)
                {
                    return "⚠ 日常工作主菜单存在，但护理记录单菜单项缺失";
                }
                else if (hasNursingRecord)
                {
                    return "⚠ 护理记录单菜单项存在，但日常工作主菜单缺失";
                }
                else
                {
                    return "✗ 菜单添加失败，未找到相关菜单项";
                }
            }
            catch (Exception ex)
            {
                return $"验证过程中发生错误：{ex.Message}";
            }
        }
        
        /// <summary>
        /// 显示当前NURINP应用的所有菜单
        /// </summary>
        /// <returns>菜单列表</returns>
        public static DataTable GetAllNurinpMenus()
        {
            try
            {
                string sql = "SELECT * FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NURINP' ORDER BY SERIAL_NO";
                DataSet result = ws.GetDataBySql(sql, "ALL_MENUS", false);
                return result.Tables[0];
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取菜单列表失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return null;
            }
        }
        
        /// <summary>
        /// 一键执行菜单更新并显示结果
        /// </summary>
        public static void ExecuteUpdate()
        {
            try
            {
                // 执行更新
                bool success = AddNursingRecordToMainMenu();
                
                if (success)
                {
                    // 验证结果
                    string validationResult = ValidateMenu();
                    
                    MessageBox.Show(
                        validationResult,
                        "菜单更新结果",
                        MessageBoxButtons.OK,
                        validationResult.StartsWith("✓") ? MessageBoxIcon.Information : MessageBoxIcon.Warning
                    );
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"执行更新时发生异常：{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
