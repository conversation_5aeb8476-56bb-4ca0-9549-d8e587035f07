using System;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using Tjhis.Nurinp.Station.Service;
using Tjhis.Nurinp.Station.Tools;

namespace Tjhis.Nurinp.Station.Tools
{
    /// <summary>
    /// 菜单更新测试程序
    /// 用于测试护理记录单菜单添加功能
    /// </summary>
    public partial class TestMenuUpdate : XtraForm
    {
        public TestMenuUpdate()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "护理记录单菜单更新测试";
            this.Size = new System.Drawing.Size(500, 400);
            this.StartPosition = FormStartPosition.CenterScreen;

            // 创建按钮面板
            var buttonPanel = new System.Windows.Forms.Panel
            {
                Height = 200,
                Dock = DockStyle.Top,
                Padding = new System.Windows.Forms.Padding(20)
            };
            this.Controls.Add(buttonPanel);

            // 测试按钮1：显示菜单更新工具
            var btnShowTool = new SimpleButton
            {
                Text = "显示菜单更新工具",
                Size = new System.Drawing.Size(200, 30),
                Location = new System.Drawing.Point(20, 20)
            };
            btnShowTool.Click += (s, e) => MenuUpdateHelper.ShowMenuUpdateTool();
            buttonPanel.Controls.Add(btnShowTool);

            // 测试按钮2：快速更新菜单
            var btnQuickUpdate = new SimpleButton
            {
                Text = "快速更新菜单",
                Size = new System.Drawing.Size(200, 30),
                Location = new System.Drawing.Point(20, 60)
            };
            btnQuickUpdate.Click += (s, e) => MenuUpdateHelper.QuickUpdateMenu();
            buttonPanel.Controls.Add(btnQuickUpdate);

            // 测试按钮3：检查菜单状态
            var btnCheckStatus = new SimpleButton
            {
                Text = "检查菜单状态",
                Size = new System.Drawing.Size(200, 30),
                Location = new System.Drawing.Point(20, 100)
            };
            btnCheckStatus.Click += (s, e) => MenuUpdateHelper.CheckMenuStatus();
            buttonPanel.Controls.Add(btnCheckStatus);

            // 测试按钮4：直接调用SimpleMenuUpdater
            var btnDirectCall = new SimpleButton
            {
                Text = "直接调用更新器",
                Size = new System.Drawing.Size(200, 30),
                Location = new System.Drawing.Point(20, 140)
            };
            btnDirectCall.Click += BtnDirectCall_Click;
            buttonPanel.Controls.Add(btnDirectCall);

            // 结果显示区域
            var resultTextBox = new System.Windows.Forms.TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Dock = DockStyle.Fill,
                Font = new System.Drawing.Font("Consolas", 9F)
            };
            this.Controls.Add(resultTextBox);

            // 保存引用以便在事件中使用
            this.Tag = resultTextBox;
        }

        private void BtnDirectCall_Click(object sender, EventArgs e)
        {
            var resultTextBox = this.Tag as System.Windows.Forms.TextBox;
            if (resultTextBox == null) return;

            try
            {
                this.Cursor = Cursors.WaitCursor;
                resultTextBox.Clear();
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 开始直接调用SimpleMenuUpdater...\r\n");

                // 1. 执行更新
                resultTextBox.AppendText("步骤1：执行菜单更新\r\n");
                bool success = SimpleMenuUpdater.AddNursingRecordToMainMenu();
                resultTextBox.AppendText($"更新结果：{(success ? "成功" : "失败")}\r\n\r\n");

                if (success)
                {
                    // 2. 验证结果
                    resultTextBox.AppendText("步骤2：验证菜单状态\r\n");
                    string validationResult = SimpleMenuUpdater.ValidateMenu();
                    resultTextBox.AppendText($"验证结果：\r\n{validationResult}\r\n\r\n");

                    // 3. 获取所有菜单
                    resultTextBox.AppendText("步骤3：获取所有NURINP菜单\r\n");
                    var menuTable = SimpleMenuUpdater.GetAllNurinpMenus();
                    if (menuTable != null && menuTable.Rows.Count > 0)
                    {
                        resultTextBox.AppendText($"找到 {menuTable.Rows.Count} 个菜单项：\r\n");
                        foreach (System.Data.DataRow row in menuTable.Rows)
                        {
                            string menuName = row["MENU_NAME"]?.ToString() ?? "";
                            string menuText = row["MENU_TEXT"]?.ToString() ?? "";
                            string serialNo = row["SERIAL_NO"]?.ToString() ?? "";
                            resultTextBox.AppendText($"- {serialNo}: {menuText} ({menuName})\r\n");
                        }
                    }
                    else
                    {
                        resultTextBox.AppendText("未找到任何菜单项\r\n");
                    }
                }

                resultTextBox.AppendText($"\r\n[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 测试完成\r\n");
            }
            catch (Exception ex)
            {
                resultTextBox.AppendText($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] 异常：{ex.Message}\r\n");
                resultTextBox.AppendText($"堆栈跟踪：\r\n{ex.StackTrace}\r\n");
            }
            finally
            {
                this.Cursor = Cursors.Default;
            }
        }

        /// <summary>
        /// 静态方法：显示测试窗体
        /// </summary>
        public static void ShowTestForm()
        {
            try
            {
                var testForm = new TestMenuUpdate();
                testForm.ShowDialog();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"显示测试窗体失败：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }

    /// <summary>
    /// 程序入口类（用于独立测试）
    /// </summary>
    public static class TestProgram
    {
        /// <summary>
        /// 测试程序主入口
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // 初始化DevExpress
                DevExpress.UserSkins.BonusSkins.Register();
                DevExpress.LookAndFeel.UserLookAndFeel.Default.SetSkinStyle("Office 2016 Colorful");

                // 显示测试窗体
                TestMenuUpdate.ShowTestForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"程序启动失败：{ex.Message}", "错误", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 命令行测试方法
        /// </summary>
        /// <param name="args">命令行参数</param>
        public static void ConsoleTest(string[] args)
        {
            Console.WriteLine("护理记录单菜单更新测试");
            Console.WriteLine("========================");
            
            try
            {
                Console.WriteLine("1. 执行菜单更新...");
                bool success = SimpleMenuUpdater.AddNursingRecordToMainMenu();
                Console.WriteLine($"   结果: {(success ? "成功" : "失败")}");

                if (success)
                {
                    Console.WriteLine("\n2. 验证菜单状态...");
                    string validationResult = SimpleMenuUpdater.ValidateMenu();
                    Console.WriteLine($"   结果: {validationResult}");

                    Console.WriteLine("\n3. 获取所有菜单...");
                    var menuTable = SimpleMenuUpdater.GetAllNurinpMenus();
                    if (menuTable != null && menuTable.Rows.Count > 0)
                    {
                        Console.WriteLine($"   找到 {menuTable.Rows.Count} 个菜单项");
                        foreach (System.Data.DataRow row in menuTable.Rows)
                        {
                            string menuName = row["MENU_NAME"]?.ToString() ?? "";
                            string menuText = row["MENU_TEXT"]?.ToString() ?? "";
                            Console.WriteLine($"   - {menuText} ({menuName})");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试异常: {ex.Message}");
            }

            Console.WriteLine("\n测试完成，按任意键退出...");
            Console.ReadKey();
        }
    }
}
