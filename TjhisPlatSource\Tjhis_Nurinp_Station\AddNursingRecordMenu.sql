-- 为住院护理站添加护理记录单到日常工作菜单
-- 执行前请确保数据库连接正常

-- 1. 首先检查是否已存在日常工作主菜单
SELECT COUNT(*) AS 主菜单存在数量 FROM COMM.SEC_MENUS_DICT 
WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN05';

-- 2. 如果主菜单不存在，先创建日常工作主菜单
INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
SELECT 'NURINP', 'NURINP_MAIN05', 'MainWindow', '', 500, '日常工作', '', 'parent', '', null, '1', '2', '', '', '住院护理主菜单', '', ''
WHERE NOT EXISTS (
    SELECT 1 FROM COMM.SEC_MENUS_DICT 
    WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN05'
);

-- 3. 添加护理记录单到日常工作菜单
INSERT INTO COMM.SEC_MENUS_DICT (APPLICATION_CODE, MENU_NAME, FORM_MENU, FORM_CONTROL, SERIAL_NO, MENU_TEXT, TOOL_TIPS, SUPPER_MENU, OPEN_FORM, OPEN_PARAM, MENU_VISIBLE, ICON_STYLE, LARGE_ICON, SMALL_ICON, MENU_GROUP, MENU_MEMOS, OPEN_FILE_NAME)
SELECT 'NURINP', 'NURINP_MAIN0570', 'MainWindow', '', 570, '护理记录单', '', 'NURINP_MAIN05', 'TjhisAppPatientView.NursingRecordSheet.frmNursingRecord', null, '1', '1', 'Images/Menu/Big/00-护理病历.png', 'Images/Menu/Small/00-护理病历.png', '住院护理主菜单', '', 'TjhisAppPatientView.dll'
WHERE NOT EXISTS (
    SELECT 1 FROM COMM.SEC_MENUS_DICT 
    WHERE APPLICATION_CODE = 'NURINP' AND MENU_NAME = 'NURINP_MAIN0570'
);

-- 4. 验证添加结果
SELECT 
    APPLICATION_CODE AS 应用代码,
    MENU_NAME AS 菜单名称,
    MENU_TEXT AS 显示文本,
    SERIAL_NO AS 序号,
    SUPPER_MENU AS 父菜单,
    OPEN_FORM AS 打开窗体,
    OPEN_FILE_NAME AS 程序集
FROM COMM.SEC_MENUS_DICT 
WHERE APPLICATION_CODE = 'NURINP' 
AND (MENU_NAME = 'NURINP_MAIN05' OR MENU_NAME = 'NURINP_MAIN0570')
ORDER BY SERIAL_NO;

-- 5. 查看所有NURINP应用的菜单（可选）
-- SELECT * FROM COMM.SEC_MENUS_DICT WHERE APPLICATION_CODE = 'NURINP' ORDER BY SERIAL_NO;

COMMIT;
