-- 为住院护理站添加护理记录单到日常工作菜单
-- 执行前请确保数据库连接正常

-- 1. 首先检查是否已存在日常工作主菜单
SELECT COUNT(*) AS 主菜单存在数量 FROM MR_APP_ENTRY
WHERE APP_CODE = 'NURINP' AND NODE_CODE = 'NURINP_MAIN05';

-- 2. 如果主菜单不存在，先创建日常工作主菜单
INSERT INTO MR_APP_ENTRY (APP_CODE, NODE_CODE, NODE_TITLE, NODE_TYPE, PARENT_NODE_CODE, SERIAL_NO, FILE_NAME, FORM_ID, ICON_FILE, HAS_TOOLBAR_ITEM, STATUS, WIN_OPEN_MODE, MEMO, WIN_PARAMETER, FORM_CONTROL, MENU_GROUP, LARGE_ICON, ICON_STYLE, OPEN_PARAM, DISPLAY_TEXT, HIS_UNIT_CODE, ICON_PRESS)
SELECT 'NURINP', 'NURINP_MAIN05', '日常工作', 'MENU', 'parent', 500, '', '', '', 0, 1, '', '住院护理主菜单', '', '', '住院护理主菜单', '', 2, '', '日常工作', 'DEFAULT', ''
WHERE NOT EXISTS (
    SELECT 1 FROM MR_APP_ENTRY
    WHERE APP_CODE = 'NURINP' AND NODE_CODE = 'NURINP_MAIN05'
);

-- 3. 添加护理记录单到日常工作菜单
INSERT INTO MR_APP_ENTRY (APP_CODE, NODE_CODE, NODE_TITLE, NODE_TYPE, PARENT_NODE_CODE, SERIAL_NO, FILE_NAME, FORM_ID, ICON_FILE, HAS_TOOLBAR_ITEM, STATUS, WIN_OPEN_MODE, MEMO, WIN_PARAMETER, FORM_CONTROL, MENU_GROUP, LARGE_ICON, ICON_STYLE, OPEN_PARAM, DISPLAY_TEXT, HIS_UNIT_CODE, ICON_PRESS)
SELECT 'NURINP', 'NURINP_MAIN0570', '护理记录单', 'FORM', 'NURINP_MAIN05', 570, 'TjhisAppPatientView.dll', 'TjhisAppPatientView.NursingRecordSheet.frmNursingRecord', 'Images/Menu/Small/00-护理病历.png', 0, 1, '', '住院护理主菜单', '', '', '住院护理主菜单', 'Images/Menu/Big/00-护理病历.png', 1, '', '护理记录单', 'DEFAULT', ''
WHERE NOT EXISTS (
    SELECT 1 FROM MR_APP_ENTRY
    WHERE APP_CODE = 'NURINP' AND NODE_CODE = 'NURINP_MAIN0570'
);

-- 4. 验证添加结果
SELECT
    APP_CODE AS 应用代码,
    NODE_CODE AS 菜单代码,
    NODE_TITLE AS 显示文本,
    SERIAL_NO AS 序号,
    PARENT_NODE_CODE AS 父菜单,
    FORM_ID AS 打开窗体,
    FILE_NAME AS 程序集
FROM MR_APP_ENTRY
WHERE APP_CODE = 'NURINP'
AND (NODE_CODE = 'NURINP_MAIN05' OR NODE_CODE = 'NURINP_MAIN0570')
ORDER BY SERIAL_NO;

-- 5. 查看所有NURINP应用的菜单（可选）
-- SELECT * FROM MR_APP_ENTRY WHERE APP_CODE = 'NURINP' ORDER BY SERIAL_NO;

COMMIT;
